import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'wouter';
import {
  UsersIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  PencilIcon,
  DocumentChartBarIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { getCurrentDate, formatDate } from '@/lib/utils';
import { getDashboardStats, getExpectedSignaturesForToday } from '@/lib/tauri-api';
import type { DashboardStats, Convict } from '@shared/schema';
import { useAuthStore } from '@/store/authStore';
import { useState } from 'react';

export default function DashboardPage() {
  const today = getCurrentDate();
  const { user } = useAuthStore();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const { data: stats, isLoading, refetch: refetchStats } = useQuery<DashboardStats>({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      console.log('🔍 Frontend: Calling getDashboardStats...');
      const result = await getDashboardStats();
      console.log('📊 Frontend: Dashboard stats received:', result);
      return result;
    },
    refetchInterval: 30000, // Auto-refresh every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  // Bugün imza atması beklenenler için veri çekme
  const { data: expectedSignatures, isLoading: isLoadingExpectedSignatures, refetch: refetchExpectedSignatures } = useQuery<Convict[]>({
    queryKey: ['expected-signatures-today'],
    queryFn: getExpectedSignaturesForToday,
    refetchInterval: 30000, // Auto-refresh every 30 seconds for real-time updates
    staleTime: 10000, // Consider data stale after 10 seconds for faster updates
  });

  // Manual refresh function
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await Promise.all([
      refetchStats(),
      refetchExpectedSignatures()
    ]);
    setIsRefreshing(false);
  };

  // Welcome message based on time
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Günaydın';
    if (hour < 18) return 'İyi günler';
    return 'İyi akşamlar';
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4 windows-scrollbar">
      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div className="space-y-1">
          <h1 className="text-sm font-normal" style={{color: 'var(--windows-text)'}}>
            {getGreeting()}, {user?.username}!
          </h1>
          <p className="text-xs text-gray-600">
            E-İmza Takip Sistemi kontrol paneli • {formatDate(today)}
          </p>
        </div>
        <Button 
          variant="windows" 
          size="windows"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="flex items-center gap-2"
        >
          <ArrowPathIcon className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link href="/convicts">
          <Card className="windows-card cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-600 flex items-center justify-center">
                    <UsersIcon className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-xs text-gray-600 mb-1">Toplam Hükümlü</p>
                  <p className="text-lg font-bold text-gray-900">
                    {stats?.total_convicts || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/signatures/expected">
          <Card className="windows-card cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-amber-600 flex items-center justify-center">
                    <ClockIcon className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-xs text-gray-600 mb-1">Bugün Beklenen İmza</p>
                  <p className="text-lg font-bold text-gray-900">
                    {stats?.expected_today || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/signatures/completed">
          <Card className="windows-card cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-emerald-600 flex items-center justify-center">
                    <CheckCircleIcon className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-xs text-gray-600 mb-1">Tamamlanan İmza</p>
                  <p className="text-lg font-bold text-gray-900">
                    {stats?.completed_today || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/reports/all-violations">
          <Card className="windows-card cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-600 flex items-center justify-center">
                    <ExclamationTriangleIcon className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-xs text-gray-600 mb-1">İhlal Sayısı</p>
                  <p className="text-lg font-bold text-gray-900">
                    {stats?.violations || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Today's Expected Signatures - Updated */}
      <Card className="windows-card mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <ClockIcon className="w-4 h-4 text-blue-600" />
              <span className="text-sm">Bugün İmza Atması Beklenenler ({formatDate(today)})</span>
            </CardTitle>
            <Link href="/signatures/record">
              <Button variant="windows" size="windows">
                <PencilIcon className="w-3 h-3 mr-1" />
                İmza Kaydet
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {isLoadingExpectedSignatures ? (
            <div className="text-center py-8 text-gray-500">
              <LoadingSpinner />
              <p className="mt-2">Yükleniyor...</p>
            </div>
          ) : expectedSignatures && expectedSignatures.length > 0 ? (
            <ul className="divide-y divide-gray-200">
              {expectedSignatures.map((convict) => (
                <li key={convict.id} className="py-3 flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {convict.first_name} {convict.last_name}
                    </p>
                    <p className="text-sm text-gray-500">TC: {convict.tc_no}</p>
                  </div>
                  <Link href={`/signatures/record?convictId=${convict.id}`}>
                    <Button variant="windows" size="windows">
                      İmzala
                    </Button>
                  </Link>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <CheckCircleIcon className="w-12 h-12 mx-auto mb-4 text-green-500" />
              <p>Bugün imza atması beklenen hükümlü bulunmamaktadır.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card className="windows-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PlusIcon className="w-4 h-4 text-green-600" />
              <span className="text-sm">Hızlı İşlemler</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link href="/convicts/new">
              <Button variant="windows" size="windows" className="w-full justify-start btn-visible">
                <UsersIcon className="w-3 h-3 mr-2" />
                Yeni Hükümlü Ekle
              </Button>
            </Link>
            <Link href="/signatures/record">
              <Button variant="windows" size="windows" className="w-full justify-start btn-primary-visible">
                <PencilIcon className="w-3 h-3 mr-2" />
                İmza Kaydet
              </Button>
            </Link>
            <Link href="/convicts">
              <Button variant="windows" size="windows" className="w-full justify-start btn-visible">
                <MagnifyingGlassIcon className="w-3 h-3 mr-2" />
                Hükümlü Ara
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="windows-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DocumentChartBarIcon className="w-4 h-4 text-blue-600" />
              <span className="text-sm">Raporlar</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link href="/reports/daily">
              <Button variant="windows" size="windows" className="w-full justify-start btn-visible">
                Günlük Rapor
              </Button>
            </Link>
            <Link href="/reports/weekly">
              <Button variant="windows" size="windows" className="w-full justify-start btn-visible">
                Haftalık Rapor
              </Button>
            </Link>
            <Link href="/reports/monthly">
              <Button variant="windows" size="windows" className="w-full justify-start btn-visible">
                Aylık Rapor
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
