import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import type { InsertUser } from '@shared/schema';

const userSchema = z.object({
  username: z
    .string()
    .min(3, 'Kullanıcı adı en az 3 karakter olmalıdır')
    .max(50, 'Kullanıcı adı en fazla 50 karakter olabilir')
    .regex(/^[a-zA-Z0-9_]+$/, 'Kullanıcı adı sadece harf, rakam ve alt çizgi içerebilir'),
  password: z
    .string()
    .min(6, '<PERSON><PERSON>re en az 6 karakter olmalıdır')
    .max(100, '<PERSON><PERSON>re en fazla 100 karakter olabilir'),
  role: z.enum(['USER', 'ADMIN'], {
    required_error: 'Rol seçiniz',
  }),
  isActive: z.boolean().default(true),
});

type UserFormData = z.infer<typeof userSchema>;

interface UserFormProps {
  initialData?: Partial<UserFormData>;
  onSubmit: (data: InsertUser) => void;
  onCancel: () => void;
  isLoading?: boolean;
  submitLabel?: string;
  isEdit?: boolean;
}

export default function UserForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = 'Kaydet',
  isEdit = false,
}: UserFormProps) {
  const form = useForm<UserFormData>({
    resolver: zodResolver(
      isEdit 
        ? userSchema.extend({
            password: z.string().min(0).optional().or(z.string().min(6)),
          })
        : userSchema
    ),
    defaultValues: {
      username: '',
      password: '',
      role: 'USER',
      isActive: true,
      ...initialData,
    },
  });

  const handleSubmit = (data: UserFormData) => {
    // Don't send empty password for updates
    const submitData: InsertUser = {
      ...data,
      ...(isEdit && !data.password && { password: undefined }),
    } as InsertUser;
    
    onSubmit(submitData);
  };

  return (
    <div className="bg-gradient-to-br from-white via-slate-50/80 to-blue-50/30 backdrop-blur-sm border border-slate-200/50 rounded-xl shadow-lg p-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Username */}
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Kullanıcı Adı <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Kullanıcı adı"
                      disabled={isEdit || isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  {!isEdit && (
                    <p className="text-xs text-slate-500 mt-1">
                      Sadece harf, rakam ve alt çizgi kullanabilirsiniz
                    </p>
                  )}
                </FormItem>
              )}
            />

            {/* Password */}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Şifre {!isEdit && <span className="text-red-500">*</span>}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder={isEdit ? "Yeni şifre (boş bırakılırsa değişmez)" : "Şifre"}
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  <p className="text-xs text-slate-500 mt-1">
                    {isEdit 
                      ? "Şifreyi değiştirmek istemiyorsanız boş bırakın"
                      : "En az 6 karakter olmalıdır"
                    }
                  </p>
                </FormItem>
              )}
            />

            {/* Role */}
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Rol <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                    <FormControl>
                      <SelectTrigger className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md">
                        <SelectValue placeholder="Rol seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="border border-slate-200 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg">
                      <SelectItem value="USER" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          Memur
                        </div>
                      </SelectItem>
                      <SelectItem value="ADMIN" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          Yönetici
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  <p className="text-xs text-slate-500 mt-1">
                    Yönetici: Tüm işlemler, Memur: Temel işlemler
                  </p>
                </FormItem>
              )}
            />
          </div>

          {/* Active Status */}
          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-6 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="space-y-1">
                  <FormLabel className="text-base font-semibold text-slate-800">Aktif Durumu</FormLabel>
                  <div className="text-sm text-slate-600">
                    Kullanıcının sisteme giriş yapabilmesini kontrol eder
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                    className="data-[state=checked]:bg-green-600"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-8 border-t border-slate-200/70">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="px-6 py-2.5 bg-white/80 border-2 border-slate-300 text-slate-600 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-slate-50 hover:border-slate-400 hover:shadow-md focus:ring-2 focus:ring-slate-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              İptal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl shadow-md hover:shadow-lg focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Kaydediliyor...
                </div>
              ) : (
                submitLabel
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
