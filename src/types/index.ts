import type { Convict, User } from '@shared/schema';

// Re-export all shared types for easier imports
export type {
  Convict,
  User,
  SignaturePeriod,
  Signature,
  InsertUser,
  InsertConvict,
  InsertSignaturePeriod,
  InsertSignature,
  ConvictWithDetails,
  DashboardStats,
  DailyReport,
} from '@shared/schema';

// Frontend-specific types
export interface SearchFilters {
  tcNo?: string;
  firstName?: string;
  lastName?: string;
  isActive?: boolean;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface ApiError {
  message: string;
  status?: number;
}

export interface FormState {
  isLoading: boolean;
  errors: Record<string, string>;
}

export interface SignatureStatusInfo {
  isRequired: boolean;
  alreadySigned: boolean;
  signatureTime?: string;
}

export interface FrequencyConfig {
  type: 'WEEKLY' | 'X_DAYS' | 'MONTHLY_SPECIFIC';
  value: any;
  referenceDate?: string;
}

// Navigation types
export interface NavItem {
  name: string;
  href?: string;
  icon?: React.ComponentType<any>;
  children?: NavItem[];
}

// Report types
export interface ReportPeriod {
  startDate: string;
  endDate: string;
}

export interface ViolationReportItem {
  convict: Convict;
  violationDate: string;
  expectedFrequency: string;
}

// Component prop types
export interface DataTableColumn<T> {
  key: keyof T | string;
  label: string;
  render?: (item: T) => React.ReactNode;
  sortable?: boolean;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

// Form validation types
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';

// Auth types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Notification types
export interface NotificationItem {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
}

// Export default empty object to make this a module
export {};
