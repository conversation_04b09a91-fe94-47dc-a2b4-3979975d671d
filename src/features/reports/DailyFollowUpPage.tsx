import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DataTable from '@/components/common/DataTable';
import { getCurrentDate, formatDate } from '@/lib/utils';
import {
  DocumentArrowDownIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import type { DailyReport } from '@shared/schema';

export default function DailyFollowUpPage() {
  const [selectedDate, setSelectedDate] = useState(getCurrentDate());
  const [hasSearched, setHasSearched] = useState(false);

  const { data: report, isLoading, refetch } = useQuery<DailyReport>({
    queryKey: ['/api/reports/daily', selectedDate],
    queryFn: async () => {
      const response = await fetch(`/api/reports/daily?date=${selectedDate}`);
      if (!response.ok) throw new Error('Rapor alınırken hata oluştu');
      return response.json();
    },
    enabled: hasSearched,
  });

  const handleGenerateReport = () => {
    setHasSearched(true);
    refetch();
  };

  const handleExportReport = () => {
    // Export functionality would be implemented here
    console.log('Rapor dışa aktarılıyor...');
  };

  const expectedColumns = [
    {
      key: 'tcNo',
      label: 'TC No',
      render: (item: any) => item.convict.tcNo,
    },
    {
      key: 'fullName',
      label: 'Ad Soyad',
      render: (item: any) => `${item.convict.firstName} ${item.convict.lastName}`,
    },
    {
      key: 'frequency',
      label: 'Beklenen Sıklık',
    },
    {
      key: 'status',
      label: 'Durum',
      render: () => (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 border border-yellow-200 rounded">
          Bekliyor
        </span>
      ),
    },
  ];

  const completedColumns = [
    {
      key: 'tcNo',
      label: 'TC No',
      render: (item: any) => item.convict.tcNo,
    },
    {
      key: 'fullName',
      label: 'Ad Soyad',
      render: (item: any) => `${item.convict.firstName} ${item.convict.lastName}`,
    },
    {
      key: 'signatureTime',
      label: 'İmza Saati',
      render: (item: any) => item.signature.signatureTime,
    },
    {
      key: 'status',
      label: 'Durum',
      render: () => (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-800 bg-green-100 border border-green-200 rounded">
          Tamamlandı
        </span>
      ),
    },
  ];

  const violationColumns = [
    {
      key: 'tcNo',
      label: 'TC No',
      render: (item: any) => item.convict.tcNo,
    },
    {
      key: 'fullName',
      label: 'Ad Soyad',
      render: (item: any) => `${item.convict.firstName} ${item.convict.lastName}`,
    },
    {
      key: 'frequency',
      label: 'Beklenen Sıklık',
    },
    {
      key: 'status',
      label: 'Durum',
      render: () => (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-800 bg-red-100 border border-red-200 rounded">
          İhlal
        </span>
      ),
    },
  ];

  return (
    <div className="windows-content">
      {/* Windows Toolbar */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="windows-title">Günlük İmza Takibi</h1>
            <span className="text-gray-600 text-xs ml-2">
              - Belirli tarih için imza durumlarını görüntüle
            </span>
          </div>
          {hasSearched && report && (
            <div className="flex items-center gap-4 text-xs text-gray-600">
              <span>Bekliyor: {report.expected.length}</span>
              <span>Tamamlandı: {report.completed.length}</span>
              <span>İhlal: {report.violations.length}</span>
            </div>
          )}
        </div>
      </div>

      <div className="p-3">
        {/* Date Selection */}
        <div className="windows-card mb-3">
          <div className="windows-section-title">Tarih Seçimi</div>
          <div className="p-3">
            <div className="flex items-end space-x-3">
              <div className="space-y-1">
                <Label htmlFor="reportDate" className="windows-label">Rapor Tarihi</Label>
                <Input
                  id="reportDate"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="windows-input"
                />
              </div>
              <Button 
                onClick={handleGenerateReport} 
                disabled={isLoading}
                className="windows-button-primary"
              >
                <MagnifyingGlassIcon className="w-4 h-4 mr-1" />
                {isLoading ? 'Getiriliyor...' : 'Raporu Getir'}
              </Button>
              {hasSearched && report && (
                <Button 
                  onClick={handleExportReport}
                  className="windows-button"
                >
                  <DocumentArrowDownIcon className="w-4 h-4 mr-1" />
                  Dışa Aktar
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Report Results */}
        {hasSearched && (
          <div className="windows-card">
            <div className="windows-section-title">
              {formatDate(selectedDate)} Tarihli Günlük Rapor
            </div>
            <div className="p-3">
              {report ? (
                <Tabs defaultValue="expected" className="w-full">
                  <TabsList className="flex bg-gray-100 p-1 rounded">
                    <TabsTrigger 
                      value="expected"
                      className="flex-1 px-3 py-1 text-xs rounded data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        İmza Atması Beklenenler ({report.expected.length})
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="completed"
                      className="flex-1 px-3 py-1 text-xs rounded data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        İmza Atanlar ({report.completed.length})
                      </div>
                    </TabsTrigger>
                    <TabsTrigger 
                      value="violations"
                      className="flex-1 px-3 py-1 text-xs rounded data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        İmza Atmayanlar ({report.violations.length})
                      </div>
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="expected" className="mt-3">
                    <DataTable
                      data={report.expected}
                      columns={expectedColumns}
                      loading={isLoading}
                      emptyMessage="Bu tarihte imza atması beklenen hükümlü bulunmuyor"
                    />
                  </TabsContent>

                  <TabsContent value="completed" className="mt-3">
                    <DataTable
                      data={report.completed}
                      columns={completedColumns}
                      loading={isLoading}
                      emptyMessage="Bu tarihte imza atan hükümlü bulunmuyor"
                    />
                  </TabsContent>

                  <TabsContent value="violations" className="mt-3">
                    <DataTable
                      data={report.violations}
                      columns={violationColumns}
                      loading={isLoading}
                      emptyMessage="Bu tarihte ihlal kaydı bulunmuyor"
                    />
                  </TabsContent>
                </Tabs>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <div className="flex flex-col items-center gap-4">
                    <div className="w-12 h-12 rounded bg-gray-100 flex items-center justify-center">
                      <MagnifyingGlassIcon className="w-6 h-6 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        {isLoading ? 'Rapor yükleniyor...' : 'Rapor bulunamadı'}
                      </p>
                      {!isLoading && (
                        <p className="text-xs text-gray-500 mt-1">
                          Seçilen tarih için veri bulunmuyor
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
