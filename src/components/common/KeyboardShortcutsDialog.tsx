import { useEffect } from 'react';
import { XMarkIcon, CommandLineIcon } from '@heroicons/react/24/outline';

interface KeyboardShortcutsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const shortcuts = [
  {
    category: 'Navigasyon',
    items: [
      { keys: ['Ctrl', 'Home'], description: 'Ana sayfaya git' },
      { keys: ['Ctrl', 'N'], description: 'Yeni hük<PERSON> e<PERSON>' },
      { keys: ['Ctrl', 'L'], description: 'Hükümlü listesini aç' },
      { keys: ['Ctrl', 'S'], description: '<PERSON>mza kaydı sayfasını aç' },
    ]
  },
  {
    category: 'Arama ve Filtreleme',
    items: [
      { keys: ['Ctrl', 'F'], description: 'Arama kutusuna odaklan' },
      { keys: ['Ctrl', 'Shift', 'F'], description: 'Filtreleme seçeneklerini aç' },
    ]
  },
  {
    category: 'Sistem',
    items: [
      { keys: ['F5'], description: '<PERSON><PERSON><PERSON><PERSON> yenile' },
      { keys: ['Ctrl', 'R'], description: 'Say<PERSON>yı yenile' },
      { keys: ['Ctrl', 'P'], description: 'Sayfayı yazdır' },
      { keys: ['F1'], description: 'Bu yardım penceresini aç' },
      { keys: ['Ctrl', 'T'], description: 'Araç çubuğunu göster/gizle' },
      { keys: ['Ctrl', 'B'], description: 'Durum çubuğunu göster/gizle' },
    ]
  },
  {
    category: 'Raporlar',
    items: [
      { keys: ['Ctrl', 'Alt', 'D'], description: 'Günlük raporu aç' },
      { keys: ['Ctrl', 'Alt', 'V'], description: 'İhlal raporu aç' },
    ]
  },
  {
    category: 'Yönetim',
    items: [
      { keys: ['Ctrl', 'Alt', 'U'], description: 'Kullanıcı yönetimi' },
      { keys: ['Ctrl', ','], description: 'Sistem ayarları' },
    ]
  },
];

export function KeyboardShortcutsDialog({ isOpen, onClose }: KeyboardShortcutsDialogProps) {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white/90 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/20">
          <div className="flex items-center space-x-3">
            <CommandLineIcon className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-slate-800">Klavye Kısayolları</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-slate-600" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {shortcuts.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-3">
              <h3 className="text-lg font-medium text-slate-700 border-b border-white/20 pb-2">
                {category.category}
              </h3>
              <div className="space-y-2">
                {category.items.map((shortcut, shortcutIndex) => (
                  <div key={shortcutIndex} className="flex items-center justify-between group">
                    <span className="text-sm text-slate-600 group-hover:text-slate-800 transition-colors">
                      {shortcut.description}
                    </span>
                    <div className="flex items-center space-x-1">
                      {shortcut.keys.map((key, keyIndex) => (
                        <div key={keyIndex} className="flex items-center">
                          <kbd className="px-2 py-1 bg-white/60 backdrop-blur-sm border border-white/30 rounded text-xs font-mono text-slate-700 shadow-sm">
                            {key}
                          </kbd>
                          {keyIndex < shortcut.keys.length - 1 && (
                            <span className="mx-1 text-slate-400">+</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-white/20 text-center">
          <p className="text-sm text-slate-600">
            Bu pencereyi kapatmak için <kbd className="px-1 bg-white/60 rounded text-xs">Esc</kbd> tuşuna basın
          </p>
        </div>
      </div>
    </div>
  );
}
