import { generateSignatureDates, formatPeriodDisplay } from '../src/lib/signature-dates.js';

// Test data - hükümlü 101'in periyotları
const testPeriods = [
  {
    id: 111,
    convict_id: 101,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'TUESDAY',
    time_start: '09:00',
    time_end: '17:00',
    allowed_days: '["MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY"]',
    is_active: true
  },
  {
    id: 112,
    convict_id: 101,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'THURSDAY',
    time_start: '09:00',
    time_end: '17:00',
    allowed_days: '["MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY"]',
    is_active: true
  },
  {
    id: 113,
    convict_id: 101,
    start_date: '2025-06-03',
    end_date: '2025-08-03',
    frequency_type: 'WEEKLY',
    frequency_value: 'SATURDAY',
    time_start: '09:00',
    time_end: '17:00',
    allowed_days: '["MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY"]',
    is_active: true
  },
  {
    id: 114,
    convict_id: 101,
    start_date: '2025-08-03',
    end_date: '2025-12-31',
    frequency_type: 'MONTHLY_SPECIFIC',
    frequency_value: '1,15,30',
    time_start: '09:00',
    time_end: '17:00',
    allowed_days: '["MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY"]',
    is_active: true
  }
];

console.log('🧪 İmza Tarihi Üretim Testi');
console.log('============================');

console.log('\n📅 Test Periyotları:');
testPeriods.forEach((period, index) => {
  console.log(`${index + 1}. ${period.start_date} - ${period.end_date}`);
  console.log(`   Tip: ${period.frequency_type}, Değer: ${period.frequency_value}`);
  console.log(`   Saat: ${period.time_start} - ${period.time_end}`);
  
  // Format display test
  const formatted = formatPeriodDisplay(period);
  console.log(`   Formatlanmış: ${formatted.frequency}, ${formatted.days}, ${formatted.time}`);
  console.log('');
});

console.log('🎯 Üretilen İmza Tarihleri:');
const generatedDates = generateSignatureDates(testPeriods, 12);
generatedDates.forEach((dateInfo, index) => {
  console.log(`${index + 1}. ${dateInfo.date} (${dateInfo.dayOfWeek}) - Periyot: ${dateInfo.period.frequency_type}`);
});

console.log(`\n📊 Toplam üretilen tarih sayısı: ${generatedDates.length}`);
console.log('✅ Test tamamlandı!');
