import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface UIState {
  isToolbarVisible: boolean;
  toggleToolbar: () => void;
  setToolbarVisible: (visible: boolean) => void;
  isStatusBarVisible: boolean;
  toggleStatusBar: () => void;
  setStatusBarVisible: (visible: boolean) => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set) => ({
      isToolbarVisible: false, // varsayılan olarak gizli
      toggleToolbar: () => set((state) => ({ isToolbarVisible: !state.isToolbarVisible })),
      setToolbarVisible: (visible: boolean) => set({ isToolbarVisible: visible }),
      isStatusBarVisible: true, // varsayılan olarak görünür
      toggleStatusBar: () => set((state) => ({ isStatusBarVisible: !state.isStatusBarVisible })),
      setStatusBarVisible: (visible: boolean) => set({ isStatusBarVisible: visible }),
    }),
    {
      name: 'ui-store',
    }
  )
); 