import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import DataTable from '@/components/common/DataTable';
import { getCurrentDate, formatDate } from '@/lib/utils';
import { getViolationsReport } from '@/lib/tauri-api';
import { useWindowsFileOperations } from '@/hooks/useWindowsFileOperations';
import {
  DocumentArrowDownIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import type { ViolationReportItem } from '@shared/schema';

export default function ViolationReportPage() {
  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setDate(date.getDate() - 7); // Default to last 7 days
    return date.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState(getCurrentDate());
  const [hasSearched, setHasSearched] = useState(false);

  // Initialize file operations hook for CSV export
  const { saveFile, loading: fileLoading, error: fileError } = useWindowsFileOperations({
    filters: [
      { name: 'CSV Dosyaları', extensions: ['csv'] },
      { name: 'Tüm Dosyalar', extensions: ['*'] }
    ]
  });

  // Preset date range functions
  const setDateRange = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);
    
    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  const setCurrentMonth = () => {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  const { data: violations, isLoading, error, refetch } = useQuery<ViolationReportItem[]>({
    queryKey: ['violations-report', startDate, endDate],
    queryFn: async () => {
      try {
        return await getViolationsReport(startDate, endDate);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        throw new Error('İhlal raporu alınırken hata oluştu: ' + errorMessage);
      }
    },
    enabled: hasSearched,
  });

  const handleGenerateReport = async () => {
    if (!startDate || !endDate) {
      alert('Lütfen başlangıç ve bitiş tarihlerini seçin');
      return;
    }

    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    if (endDateObj < startDateObj) {
      alert('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
      return;
    }

    // Check if date range is too large (more than 1 year)
    const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 365) {
      const confirm = window.confirm(
        `Seçilen tarih aralığı ${diffDays} gün (${Math.round(diffDays/30)} ay). ` +
        'Bu büyük tarih aralığı için rapor oluşturma uzun sürebilir. Devam etmek istiyor musunuz?'
      );
      if (!confirm) return;
    }

    setHasSearched(true);
    refetch();
  };

  const handleExportReport = async () => {
    if (!violations || violations.length === 0) {
      alert('Dışa aktarılacak veri bulunamadı');
      return;
    }
    
    try {
      // Create CSV content with Turkish characters support
      const headers = ['Hükümlü TC', 'Hükümlü Adı Soyadı', 'İhlal Tarihi', 'Beklenen Sıklık'];
      const csvContent = [
        headers.join(','),
        ...violations.map(item => [
          item.convict.tc_no,
          `"${item.convict.first_name} ${item.convict.last_name}"`,
          formatDate(item.violationDate),
          `"${item.expectedFrequency}"`
        ].join(','))
      ].join('\n');

      // Add BOM for Turkish characters
      const BOM = '\uFEFF';
      const csvWithBOM = BOM + csvContent;

      // Use Tauri dialog to save file
      const suggestedFileName = `ihlal-raporu-${formatDate(startDate)}-${formatDate(endDate)}.csv`;
      const savedPath = await saveFile(csvWithBOM, suggestedFileName);
      
      if (savedPath) {
        alert(`İhlal raporu başarıyla kaydedildi!\nKonum: ${savedPath}`);
      }
      // If savedPath is null, user canceled the dialog - no need to show message
      
    } catch (error) {
      console.error('Export error:', error);
      alert('Dosya kaydedilirken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  const columns = [
    {
      key: 'tcNo',
      label: 'Hükümlü TC',
      render: (item: ViolationReportItem) => item.convict.tc_no,
    },
    {
      key: 'fullName',
      label: 'Hükümlü Adı Soyadı',
      render: (item: ViolationReportItem) => 
        `${item.convict.first_name} ${item.convict.last_name}`,
    },
    {
      key: 'violationDate',
      label: 'İhlal Tarihi',
      render: (item: ViolationReportItem) => formatDate(item.violationDate),
    },
    {
      key: 'expectedFrequency',
      label: 'O Gün Beklenen Sıklık',
      render: (item: ViolationReportItem) => item.expectedFrequency,
    },
    {
      key: 'status',
      label: 'Durum',
      render: () => (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 border border-red-200 rounded">
          🚫 İhlal
        </span>
      ),
    },
  ];

  return (
    <div className="windows-content">
      {/* Windows Toolbar */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="windows-title">İmza İhlal Raporu</h1>
            <span className="text-gray-600 text-xs ml-2">
              - Belirli tarih aralığındaki imza ihlallerini görüntüle ve analiz et
            </span>
          </div>
          <div className="text-xs text-gray-600">
            Bugün: {formatDate(getCurrentDate())}
          </div>
        </div>
      </div>

      <div className="p-3">
        {/* Date Range Selection */}
        <div className="windows-card mb-3">
          <div className="windows-section-title">Tarih Aralığı Seçimi</div>
          <div className="p-3">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
              <div className="space-y-1">
                <Label htmlFor="startDate" className="windows-label">Başlangıç Tarihi</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="windows-input"
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="endDate" className="windows-label">Bitiş Tarihi</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="windows-input"
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={handleGenerateReport} 
                  disabled={isLoading}
                  className="windows-button-primary flex-1"
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-1" />
                  {isLoading ? 'Getiriliyor...' : 'Raporu Getir'}
                </Button>
                {hasSearched && violations && (
                  <Button 
                    onClick={handleExportReport}
                    disabled={fileLoading}
                    className="windows-button"
                  >
                    <DocumentArrowDownIcon className="w-4 h-4 mr-1" />
                    {fileLoading ? 'Kaydediliyor...' : 'Dışa Aktar'}
                  </Button>
                )}
              </div>
            </div>
            <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700 border border-blue-200">
              Rapor {formatDate(startDate)} - {formatDate(endDate)} tarihleri arası ihlalleri gösterecektir
            </div>

            {/* File Operation Error Display */}
            {fileError && (
              <div className="mt-2 p-2 bg-red-50 rounded text-xs text-red-700 border border-red-200">
                <strong>Dosya Hatası:</strong> {fileError}
              </div>
            )}
            
            {/* Quick Date Presets */}
            <div className="mt-3 pt-2 border-t border-gray-200">
              <div className="text-xs text-gray-600 mb-2">Hızlı Tarih Seçimi:</div>
              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => setDateRange(7)}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  Son 7 Gün
                </Button>
                <Button
                  onClick={() => setDateRange(30)}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  Son 30 Gün
                </Button>
                <Button
                  onClick={() => setCurrentMonth()}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  Bu Ay
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="windows-card mb-3">
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <div className="text-red-700 text-sm">
                ❌ Hata: {error.message}
              </div>
            </div>
          </div>
        )}

        {/* Report Summary */}
        {hasSearched && violations && violations.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
            <div className="windows-card text-center">
              <div className="p-3">
                <div className="text-red-600 font-bold text-2xl mb-1">⚠️</div>
                <div className="windows-label">Toplam İhlal</div>
                <div className="text-lg font-bold text-red-600">{violations.length}</div>
              </div>
            </div>

            <div className="windows-card text-center">
              <div className="p-3">
                <div className="text-blue-600 font-bold text-2xl mb-1">👤</div>
                <div className="windows-label">İhlal Yapan Hükümlü</div>
                <div className="text-lg font-bold text-blue-600">
                  {new Set(violations.map(v => v.convict.id)).size}
                </div>
              </div>
            </div>

            <div className="windows-card text-center">
              <div className="p-3">
                <div className="text-orange-600 font-bold text-2xl mb-1">📅</div>
                <div className="windows-label">Farklı İhlal Günü</div>
                <div className="text-lg font-bold text-orange-600">
                  {new Set(violations.map(v => v.violationDate)).size}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Violation Report Results */}
        {hasSearched && (
          <div className="windows-card">
            <div className="windows-section-title">
              İhlal Raporu Detayları
              {violations && (
                <span className="text-xs font-normal text-gray-500 ml-2">
                  ({formatDate(startDate)} - {formatDate(endDate)})
                </span>
              )}
            </div>
            <div className="p-3">
              <DataTable
                data={violations || []}
                columns={columns}
                loading={isLoading}
                emptyMessage={
                  hasSearched 
                    ? violations && violations.length === 0
                      ? "✅ Seçilen tarih aralığında ihlal kaydı bulunmuyor - Bu iyi bir haber!" 
                      : "Veri yükleniyor..."
                    : "📅 Rapor oluşturmak için tarih aralığı seçip 'Raporu Getir' butonuna tıklayın"
                }
              />
              {hasSearched && !isLoading && violations && violations.length > 0 && (
                <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600 border">
                  ℹ️ Toplam {violations.length} ihlal kaydı bulundu. 
                  Bu raporu CSV formatında dışa aktarmak için "Dışa Aktar" butonunu kullanabilirsiniz.
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
