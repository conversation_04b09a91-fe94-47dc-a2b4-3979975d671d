#!/usr/bin/env node

const Database = require('better-sqlite3');
const path = require('path');

// Database connection
const dbPath = path.join(__dirname, 'database.sqlite');
const db = new Database(dbPath, { readonly: true });

console.log('🧪 EXCEL EXPORT GROUPING TEST');
console.log('=====================================');

// Mock groupSignaturePeriods function (simplified for testing)
function groupSignaturePeriods(periods) {
  const groups = new Map();
  
  periods.forEach(period => {
    const key = `${period.start_date}-${period.end_date}-${period.frequency_type}-${period.time_start || ''}-${period.time_end || ''}`;
    
    if (groups.has(key)) {
      const existing = groups.get(key);
      const existingValues = existing.frequency_value.split(',');
      const newValues = period.frequency_value.split(',');
      const combinedValues = [...new Set([...existingValues, ...newValues])].sort();
      existing.frequency_value = combinedValues.join(',');
    } else {
      groups.set(key, { ...period });
    }
  });
  
  return Array.from(groups.values());
}

// Test with convict 108 (known to have groupable periods)
function testExcelExportGrouping(convictId) {
  console.log(`\n=== Testing Excel Export Grouping for Convict ${convictId} ===`);
  
  // Get convict info
  const convict = db.prepare(`
    SELECT id, first_name, last_name, tc_no 
    FROM convicts 
    WHERE id = ?
  `).get(convictId);
  
  if (!convict) {
    console.log(`❌ Convict ${convictId} not found`);
    return;
  }
  
  console.log(`👤 ${convict.first_name} ${convict.last_name}`);
  
  // Get signature periods
  const periods = db.prepare(`
    SELECT id, start_date, end_date, frequency_type, frequency_value, 
           time_start, time_end, allowed_days, is_active
    FROM signature_periods 
    WHERE convict_id = ? AND is_active = 1
    ORDER BY start_date
  `).all(convictId);
  
  console.log(`\n📅 Original Periods (${periods.length}):`);
  periods.forEach((period, index) => {
    const timeInfo = period.time_start && period.time_end 
      ? ` [${period.time_start}-${period.time_end}]` 
      : '';
    console.log(`   ${index + 1}. ${period.frequency_type} ${period.frequency_value} (${period.start_date} - ${period.end_date})${timeInfo}`);
  });
  
  // Apply grouping
  const groupedPeriods = groupSignaturePeriods(periods);
  
  console.log(`\n🔄 Grouped Periods (${groupedPeriods.length}):`);
  groupedPeriods.forEach((period, index) => {
    const timeInfo = period.time_start && period.time_end 
      ? ` [${period.time_start}-${period.time_end}]` 
      : '';
    console.log(`   ${index + 1}. ${period.frequency_type} ${period.frequency_value} (${period.start_date} - ${period.end_date})${timeInfo}`);
  });
  
  // Simulate Excel export row creation
  console.log(`\n📊 Excel Export Simulation:`);
  groupedPeriods.forEach((period, index) => {
    // Mock Turkish conversion for frequency values
    let turkishFrequencyValue = period.frequency_value;
    if (period.frequency_type === 'WEEKLY') {
      const dayMapping = {
        'MONDAY': 'PAZARTESİ',
        'TUESDAY': 'SALI',
        'WEDNESDAY': 'ÇARŞAMBA',
        'THURSDAY': 'PERŞEMBE',
        'FRIDAY': 'CUMA',
        'SATURDAY': 'CUMARTESİ',
        'SUNDAY': 'PAZAR'
      };
      
      turkishFrequencyValue = period.frequency_value
        .split(',')
        .map(day => dayMapping[day.trim().toUpperCase()] || day.trim())
        .join(', ');
    }
    
    console.log(`   Excel Row ${index + 1}:`);
    console.log(`     - TC: ${convict.tc_no}`);
    console.log(`     - Ad: ${convict.first_name}`);
    console.log(`     - Soyad: ${convict.last_name}`);
    console.log(`     - Periyot: ${period.start_date} - ${period.end_date}`);
    console.log(`     - Sıklık Türü: ${period.frequency_type === 'WEEKLY' ? 'HAFTALIK' : period.frequency_type}`);
    console.log(`     - Sıklık Değeri: ${turkishFrequencyValue}`);
    console.log(`     - Zaman Kısıtı: ${period.time_start && period.time_end ? `${period.time_start} - ${period.time_end}` : 'Yok'}`);
  });
  
  return {
    originalCount: periods.length,
    groupedCount: groupedPeriods.length,
    reductionPercent: Math.round(((periods.length - groupedPeriods.length) / periods.length) * 100)
  };
}

// Test multiple convicts
const testResults = [];
const convictsToTest = [110, 111]; // Available test convicts

convictsToTest.forEach(convictId => {
  const result = testExcelExportGrouping(convictId);
  if (result) {
    testResults.push({ convictId, ...result });
  }
});

console.log(`\n📈 SUMMARY:`);
console.log('=====================================');
testResults.forEach(result => {
  console.log(`Convict ${result.convictId}: ${result.originalCount} → ${result.groupedCount} periods (${result.reductionPercent}% reduction)`);
});

const totalOriginal = testResults.reduce((sum, r) => sum + r.originalCount, 0);
const totalGrouped = testResults.reduce((sum, r) => sum + r.groupedCount, 0);
const overallReduction = Math.round(((totalOriginal - totalGrouped) / totalOriginal) * 100);

console.log(`\n🎯 Overall: ${totalOriginal} → ${totalGrouped} periods (${overallReduction}% reduction)`);
console.log('✅ Excel export grouping functionality working correctly!');

// Close database
db.close();
