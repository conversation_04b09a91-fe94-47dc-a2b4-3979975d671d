// Simple test to verify exemption duplicate fix
const Database = require('better-sqlite3');

function testExemptionDuplicateFix() {
    console.log('🧪 Testing Exemption Duplicate Fix');
    console.log('==================================');
    
    const dbPath = '/Users/<USER>/eits/database.sqlite';
    
    try {
        const db = new Database(dbPath);
        
        // Test data - duplicate exemption
        const testConvictId = 122; // Valid convict ID from database
        const testExemption = {
            convict_id: testConvictId,
            exemption_type: 'TEST_EXEMPTION',
            start_date: '2025-01-01',
            end_date: '2025-01-31',
            description: 'Test exemption for duplicate fix',
            document_path: null,
            created_by: 3, // Valid user ID (admin)
            is_active: 1
        };
        
        console.log(`\n📝 Testing with convict ID: ${testConvictId}`);
        
        // Check existing exemptions
        const existingExemptions = db.prepare(
            'SELECT * FROM exemptions WHERE convict_id = ? AND exemption_type = ? AND start_date = ? AND end_date = ?'
        ).all(testExemption.convict_id, testExemption.exemption_type, testExemption.start_date, testExemption.end_date);
        
        console.log(`🔍 Found ${existingExemptions.length} existing exemptions matching criteria`);
        
        if (existingExemptions.length > 0) {
            console.log('   Existing exemption found - testing update logic');
        } else {
            console.log('   No existing exemption - testing insert logic');
            
            // Insert the test exemption first
            const insertResult = db.prepare(`
                INSERT INTO exemptions (convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `).run(
                testExemption.convict_id,
                testExemption.exemption_type, 
                testExemption.start_date,
                testExemption.end_date,
                testExemption.description,
                testExemption.document_path,
                testExemption.created_by,
                testExemption.is_active
            );
            
            console.log(`✅ Successfully inserted exemption with ID: ${insertResult.lastInsertRowid}`);
        }
        
        // Now test the bulk insert which should handle duplicates
        console.log('\n🔄 Testing bulk insert with duplicate...');
        
        // Simulate the bulk insert scenario that was failing
        const bulkExemptions = [
            {
                convict_id: testExemption.convict_id,
                exemption_type: testExemption.exemption_type,
                start_date: testExemption.start_date,
                end_date: testExemption.end_date,
                description: 'Updated description via bulk import',
                document_path: null,
                created_by: 3, // Valid user ID (admin)
                is_active: 1
            }
        ];
        
        // Test the duplicate handling logic manually
        for (const exemption of bulkExemptions) {
            const existingCount = db.prepare(`
                SELECT COUNT(*) as count FROM exemptions 
                WHERE convict_id = ? AND exemption_type = ? AND start_date = ? AND end_date = ? AND is_active = 1
            `).get(exemption.convict_id, exemption.exemption_type, exemption.start_date, exemption.end_date).count;
            
            if (existingCount > 0) {
                console.log('🔄 Duplicate detected - would update existing exemption');
                
                // Find the existing exemption
                const existing = db.prepare(`
                    SELECT id FROM exemptions 
                    WHERE convict_id = ? AND exemption_type = ? AND start_date = ? AND end_date = ? AND is_active = 1 
                    LIMIT 1
                `).get(exemption.convict_id, exemption.exemption_type, exemption.start_date, exemption.end_date);
                
                if (existing) {
                    // Update the existing exemption
                    const updateResult = db.prepare(`
                        UPDATE exemptions SET description = ?, document_path = ?, created_by = ?, updated_at = CURRENT_TIMESTAMP 
                        WHERE id = ?
                    `).run(exemption.description, exemption.document_path, exemption.created_by, existing.id);
                    
                    console.log(`✅ Successfully updated existing exemption ID: ${existing.id} (${updateResult.changes} changes)`);
                }
            } else {
                console.log('➕ No duplicate - would insert new exemption');
            }
        }
        
        // Verify final state
        const finalExemptions = db.prepare(
            'SELECT * FROM exemptions WHERE convict_id = ? AND exemption_type = ? AND start_date = ? AND end_date = ?'
        ).all(testExemption.convict_id, testExemption.exemption_type, testExemption.start_date, testExemption.end_date);
        
        console.log(`\n📊 Final state: ${finalExemptions.length} exemption(s) for test criteria`);
        finalExemptions.forEach((exemption, index) => {
            console.log(`   ${index + 1}. ID: ${exemption.id}, Description: "${exemption.description}"`);
        });
        
        // Cleanup
        console.log('\n🧹 Cleaning up test data...');
        const deleteResult = db.prepare(
            'DELETE FROM exemptions WHERE convict_id = ? AND exemption_type = ?'
        ).run(testExemption.convict_id, testExemption.exemption_type);
        console.log(`   Deleted ${deleteResult.changes} test exemption(s)`);
        
        console.log('\n✅ Exemption duplicate fix test completed successfully!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    }
}

// Run the test
testExemptionDuplicateFix();
