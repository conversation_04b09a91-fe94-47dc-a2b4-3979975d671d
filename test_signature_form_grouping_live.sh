#!/bin/bash

echo "🧪 Signature Form Grouping - Live Test"
echo "======================================"

# Test convict 108 - Should have 2 grouped periods
echo -e "\n📊 Convict 108 - <PERSON><PERSON>:"
echo "Expected: 2 grouped periods"
echo "  1. 2025-06-03 to 2025-08-03: Haftada 3 Gün (Salı, Perşembe, Cumartesi)"
echo "  2. 2025-08-03 to 2025-12-31: <PERSON><PERSON><PERSON> 3 <PERSON>ün (1, 15, 30. gün<PERSON><PERSON>)"

sqlite3 database.sqlite "SELECT 'Raw periods:', COUNT(*) FROM signature_periods WHERE convict_id = 108 AND is_active = 1;"

echo -e "\nRaw signature periods for convict 108:"
sqlite3 database.sqlite ".mode column" ".headers on" "SELECT start_date, end_date, frequency_type, frequency_value, time_start, time_end FROM signature_periods WHERE convict_id = 108 AND is_active = 1;"

# Test convict 109 - Should have 1 grouped period
echo -e "\n📊 Convict 109 - <PERSON>eynep Güngör:"
echo "Expected: 1 grouped period"
echo "  1. 2025-05-01 to 2025-07-01: Haftada 2 <PERSON>ü<PERSON> (Pazartesi, Perşembe)"

sqlite3 database.sqlite "SELECT 'Raw periods:', COUNT(*) FROM signature_periods WHERE convict_id = 109 AND is_active = 1;"

echo -e "\nRaw signature periods for convict 109:"
sqlite3 database.sqlite ".mode column" ".headers on" "SELECT start_date, end_date, frequency_type, frequency_value, time_start, time_end FROM signature_periods WHERE convict_id = 109 AND is_active = 1;"

echo -e "\n🎯 Test Results:"
echo "✅ Database data is prepared"
echo "✅ Grouping logic is implemented"
echo "🔍 Please check the signature forms at:"
echo "   - http://localhost:1420/convicts/108/signature-form"
echo "   - http://localhost:1420/convicts/109/signature-form"

echo -e "\n📋 Expected Results in UI:"
echo "Convict 108 should show:"
echo "  03.06.2025 - 03.08.2025"
echo "  Haftada 3 Gün"
echo "  Salı, Perşembe, Cumartesi"
echo "  Saat 09:00 - 17:00"
echo ""
echo "  03.08.2025 - 31.12.2025"
echo "  Ayda 3 Gün"
echo "  1, 15, 30. günlerde"
echo "  Saat 09:00 - 17:00"

echo -e "\nConvict 109 should show:"
echo "  01.05.2025 - 01.07.2025"
echo "  Haftada 2 Gün"
echo "  Pazartesi, Perşembe"
echo "  Saat 09:00 - 17:00"
