name: Build Windows Application

on:
  push:
    branches: [ main, release ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-windows:
    runs-on: windows-latest
    
    strategy:
      matrix:
        target: [x86_64-pc-windows-msvc]
        # Add more targets if needed: i686-pc-windows-msvc, aarch64-pc-windows-msvc
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        toolchain: stable-msvc
        targets: ${{ matrix.target }}

    - name: Cache Rust dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/bin/
          ~/.cargo/registry/index/
          ~/.cargo/registry/cache/
          ~/.cargo/git/db/
          src-tauri/target/
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}

    - name: Install frontend dependencies
      run: pnpm install

    - name: Build frontend
      run: pnpm run build

    - name: Build Tauri application
      run: pnpm tauri build --target ${{ matrix.target }}
      env:
        TAURI_PRIVATE_KEY: ${{ secrets.TAURI_PRIVATE_KEY }}
        TAURI_KEY_PASSWORD: ${{ secrets.TAURI_KEY_PASSWORD }}

    - name: Upload MSI artifact
      uses: actions/upload-artifact@v4
      with:
        name: eits-windows-msi-${{ matrix.target }}
        path: src-tauri/target/${{ matrix.target }}/release/bundle/msi/*.msi

    - name: Upload NSIS artifact
      uses: actions/upload-artifact@v4
      with:
        name: eits-windows-nsis-${{ matrix.target }}
        path: src-tauri/target/${{ matrix.target }}/release/bundle/nsis/*.exe

    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/v')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          src-tauri/target/${{ matrix.target }}/release/bundle/msi/*.msi
          src-tauri/target/${{ matrix.target }}/release/bundle/nsis/*.exe
        draft: true
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  build-multiple-architectures:
    runs-on: windows-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    strategy:
      matrix:
        target: 
          - x86_64-pc-windows-msvc
          - i686-pc-windows-msvc
          - aarch64-pc-windows-msvc
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        toolchain: stable-msvc
        targets: ${{ matrix.target }}

    - name: Install ARM64 build tools (if needed)
      if: matrix.target == 'aarch64-pc-windows-msvc'
      run: |
        # This requires Visual Studio with ARM64 build tools
        echo "ARM64 target requires Visual Studio with ARM64 build tools"

    - name: Install dependencies
      run: pnpm install

    - name: Build application
      run: pnpm tauri build --target ${{ matrix.target }}

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: eits-${{ matrix.target }}
        path: |
          src-tauri/target/${{ matrix.target }}/release/bundle/msi/*.msi
          src-tauri/target/${{ matrix.target }}/release/bundle/nsis/*.exe
