import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { validateTcNo } from '@/lib/utils';
import type { InsertConvict } from '@shared/schema';

const convictSchema = z.object({
  tcNo: z
    .string()
    .min(11, 'TC Kimlik No 11 haneli olmalıdır')
    .max(11, 'TC Kimlik No 11 haneli olmalıdır')
    .regex(/^\d+$/, 'TC Kimlik No sadece rakam içermelidir')
    .refine(validateTcNo, 'Geçersiz TC Kimlik No'),
  firstName: z.string().min(1, 'Ad gereklidir'),
  lastName: z.string().min(1, 'Soyad gereklidir'),
  supervisionStartDate: z.string().min(1, 'Denetim başlangıç tarihi gereklidir'),
  supervisionEndDate: z.string().min(1, 'Denetim bitiş tarihi gereklidir'),
  phoneNumber: z.string().optional(),
  relativePhoneNumber: z.string().optional(),
  address: z.string().optional(),
  fileNumber: z.string().optional(),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
}).refine(
  (data) => new Date(data.supervisionEndDate) > new Date(data.supervisionStartDate),
  {
    message: 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır',
    path: ['supervisionEndDate'],
  }
);

type ConvictFormData = z.infer<typeof convictSchema>;

interface ConvictFormProps {
  initialData?: Partial<ConvictFormData>;
  onSubmit: (data: InsertConvict) => void;
  onCancel: () => void;
  isLoading?: boolean;
  submitLabel?: string;
  isEdit?: boolean;
}

export default function ConvictForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = 'Kaydet',
  isEdit = false,
}: ConvictFormProps) {
  const form = useForm<ConvictFormData>({
    resolver: zodResolver(convictSchema),
    defaultValues: {
      tcNo: '',
      firstName: '',
      lastName: '',
      supervisionStartDate: '',
      supervisionEndDate: '',
      phoneNumber: '',
      relativePhoneNumber: '',
      address: '',
      fileNumber: '',
      isActive: true,
      notes: '',
      ...initialData,
    },
  });

  const handleSubmit = (data: ConvictFormData) => {
    // Map camelCase form data to snake_case API data
    const submitData: InsertConvict = {
      tc_no: data.tcNo,
      first_name: data.firstName,
      last_name: data.lastName,
      supervision_start_date: data.supervisionStartDate,
      supervision_end_date: data.supervisionEndDate,
      phone_number: data.phoneNumber,
      relative_phone_number: data.relativePhoneNumber,
      address: data.address,
      file_number: data.fileNumber,
      is_active: data.isActive,
      notes: data.notes,
    };
    onSubmit(submitData);
  };

  return (
    <div className="bg-gradient-to-br from-white via-slate-50/80 to-blue-50/30 backdrop-blur-sm border border-slate-200/50 rounded-xl shadow-lg p-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* TC Kimlik No */}
            <FormField
              control={form.control}
              name="tcNo"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    TC Kimlik No <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="11 haneli TC Kimlik No"
                      maxLength={11}
                      disabled={isEdit || isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  {!isEdit && (
                    <p className="text-xs text-slate-500 mt-1">11 haneli TC Kimlik Numarası giriniz</p>
                  )}
                </FormItem>
              )}
            />

            {/* Ad */}
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Ad <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ad"
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                </FormItem>
              )}
            />

            {/* Soyad */}
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Soyad <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Soyad"
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                </FormItem>
              )}
            />

            {/* Denetim Başlangıç Tarihi */}
            <FormField
              control={form.control}
              name="supervisionStartDate"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Denetim Başlangıç Tarihi <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                </FormItem>
              )}
            />

            {/* Denetim Bitiş Tarihi */}
            <FormField
              control={form.control}
              name="supervisionEndDate"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Denetim Bitiş Tarihi <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  <p className="text-xs text-slate-500 mt-1">Başlangıç tarihinden sonra olmalıdır</p>
                </FormItem>
              )}
            />

            {/* Telefon Numarası */}
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Telefon Numarası
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="0555 123 45 67"
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                </FormItem>
              )}
            />

            {/* Yakın Telefon Numarası */}
            <FormField
              control={form.control}
              name="relativePhoneNumber"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Yakın Telefon Numarası
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="0555 987 65 43"
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                </FormItem>
              )}
            />
          </div>

          {/* Adres - Full width */}
          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                  Adres
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Ev adresi"
                    disabled={isLoading}
                    rows={3}
                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
              </FormItem>
            )}
          />

          {/* Dosya Numarası - Full width */}
          <FormField
            control={form.control}
            name="fileNumber"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                  Dosya Numarası
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Örn: 2025/34 NKL"
                    disabled={isLoading}
                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                <p className="text-xs text-slate-500 mt-1">Hükümlünün dava dosya numarası</p>
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Aktif Durumu (sadece düzenleme modunda) */}
            {isEdit && (
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-6 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="space-y-1">
                      <FormLabel className="text-base font-semibold text-slate-800">Aktif Durumu</FormLabel>
                      <div className="text-sm text-slate-600">
                        Hükümlünün denetim durumunu belirler
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                        className="data-[state=checked]:bg-green-600"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}
          </div>

          {/* Notlar */}
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">Notlar</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Hükümlü ile ilgili isteğe bağlı notlar..."
                    rows={4}
                    disabled={isLoading}
                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                <p className="text-xs text-slate-500 mt-1">İsteğe bağlı - ek bilgiler ekleyebilirsiniz</p>
              </FormItem>
            )}
          />

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-8 border-t border-slate-200/70">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="px-6 py-2.5 bg-white/80 border-2 border-slate-300 text-slate-600 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-slate-50 hover:border-slate-400 hover:shadow-md focus:ring-2 focus:ring-slate-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              İptal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl shadow-md hover:shadow-lg focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Kaydediliyor...
                </div>
              ) : (
                submitLabel
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
