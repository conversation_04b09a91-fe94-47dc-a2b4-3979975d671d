# Contact Fields Testing Plan

## Overview
Testing the implementation of three new contact fields for convict records:
1. Phone Number (Hükümlü Telefon Numarası)
2. <PERSON><PERSON>'s Phone Number (Yakınına Ait Telefon Numarası)  
3. Address (Adres Bilgileri)

## Test Scenarios

### 1. Excel Import Testing
- **Objective**: Verify that Excel files with new contact fields can be imported successfully
- **Test File**: `test_contact_fields.xlsx`
- **Expected**: New convicts should be created with contact information populated

### 2. Excel Export Testing
- **Objective**: Verify that exported Excel files include new contact fields
- **Expected**: Export should contain columns for all three contact fields with data

### 3. Form Input Testing
- **Objective**: Test manual entry of convicts with contact information
- **Scenarios**:
  - Add new convict with all contact fields filled
  - Add new convict with some contact fields empty (optional validation)
  - Add new convict with no contact fields (all optional)

### 4. Edit Functionality Testing
- **Objective**: Test editing existing convicts to add/modify contact information
- **Expected**: Contact fields should be editable and save correctly

### 5. Display Testing
- **Objective**: Verify contact information displays correctly in signature recording
- **Expected**: Contact information should appear in convict details when available

### 6. Validation Testing
- **Objective**: Test form validation for contact fields
- **Scenarios**:
  - Valid phone number formats
  - Invalid phone number formats (if validation exists)
  - Long address text handling
  - Special characters in fields

## Test Results

### Database Schema Verification ✅
- Migration applied successfully
- New columns added: phone_number, relative_phone_number, address
- All columns are TEXT type and nullable

### Compilation Status ✅
- Rust backend compiles without errors
- TypeScript frontend compiles without errors
- Application runs successfully at http://localhost:1420

### Test Data Preparation ✅
- Created `test_contact_fields.csv` with sample data
- Converted to `test_contact_fields.xlsx` format
- Test data includes Turkish phone numbers and addresses

## Next Steps
1. Execute Excel import test
2. Execute Excel export test
3. Execute form input tests
4. Execute edit functionality tests
5. Execute display tests
6. Execute validation tests
7. Document all results
