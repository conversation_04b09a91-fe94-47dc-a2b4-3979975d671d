import * as React from "react";
import { cn } from "@/lib/utils";
import { useWindowsContextMenu } from "./windows-context-menu";

interface WindowsMenuItemData {
  id: string;
  label: string;
  icon?: React.ReactNode;
  shortcut?: string;
  onClick?: () => void;
  disabled?: boolean;
  separator?: boolean;
  children?: WindowsMenuItemData[];
}

interface WindowsMenuProps {
  items: WindowsMenuItemData[];
  className?: string;
}

interface WindowsMenuItemProps {
  item: WindowsMenuItemData;
  isActive: boolean;
  onActivate: () => void;
  onDeactivate: () => void;
}

export function WindowsMenu({ items, className }: WindowsMenuProps) {
  const [activeSubmenu, setActiveSubmenu] = React.useState<string | null>(null);
  const menuRef = React.useRef<HTMLDivElement>(null);
  
  // Close menu when clicking outside
  React.useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        setActiveSubmenu(null);
      }
    };
    
    document.addEventListener("mousedown", handleOutsideClick);
    return () => document.removeEventListener("mousedown", handleOutsideClick);
  }, []);
  
  return (
    <div 
      ref={menuRef}
      className={cn(
        "flex items-center bg-gray-100 border-b border-gray-300", 
        className
      )}
    >
      {items.map((item) => (
        <WindowsMenuItem 
          key={item.id} 
          item={item} 
          isActive={activeSubmenu === item.id}
          onActivate={() => setActiveSubmenu(item.id)}
          onDeactivate={() => setActiveSubmenu(null)}
        />
      ))}
    </div>
  );
}

function WindowsMenuItem({ item, isActive, onActivate, onDeactivate }: WindowsMenuItemProps) {
  const hasSubmenu = item.children && item.children.length > 0;
  const submenuRef = React.useRef<HTMLDivElement>(null);
  
  // Set up context menu for right-click
  const contextItems = item.children?.map(child => ({
    id: child.id,
    label: child.label,
    icon: child.icon,
    onClick: child.onClick,
    disabled: child.disabled,
    separator: child.separator,
    shortcut: child.shortcut
  })) || [];
  
  const { handleContextMenu, isOpen, contextMenuProps } = useWindowsContextMenu(contextItems);
  
  const handleClick = () => {
    if (hasSubmenu) {
      onActivate();
    } else if (item.onClick) {
      item.onClick();
      onDeactivate();
    }
  };
  
  // Close submenu when mouse leaves both the menu item and submenu
  const handleMouseLeave = (e: React.MouseEvent) => {
    if (
      !submenuRef.current?.contains(e.relatedTarget as Node) && 
      !(e.currentTarget as HTMLElement).contains(e.relatedTarget as Node)
    ) {
      onDeactivate();
    }
  };
  
  return (
    <div className="relative" onMouseLeave={handleMouseLeave}>
      <div
        className={cn(
          "px-3 py-1 text-xs cursor-default select-none",
          isActive ? "bg-blue-600 text-white" : "hover:bg-gray-200"
        )}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
      >
        {item.label}
      </div>
      
      {hasSubmenu && isActive && (
        <div 
          ref={submenuRef}
          className="absolute top-full left-0 z-50 windows-context-menu min-w-[180px] py-1"
        >
          {item.children?.map((child) => (
            <React.Fragment key={child.id}>
              {child.separator ? (
                <div className="windows-context-menu-separator" />
              ) : (
                <div
                  className={cn(
                    "windows-context-menu-item",
                    child.disabled && "opacity-50 pointer-events-none"
                  )}
                  onClick={() => {
                    if (!child.disabled && child.onClick) {
                      child.onClick();
                      onDeactivate();
                    }
                  }}
                >
                  {child.icon && <span className="w-4 h-4">{child.icon}</span>}
                  <span className="flex-1">{child.label}</span>
                  {child.shortcut && (
                    <span className="text-gray-500 text-xs ml-4">{child.shortcut}</span>
                  )}
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      )}
      
      {isOpen && (
        <div 
          className="windows-context-menu"
          style={{ 
            position: "fixed",
            left: `${contextMenuProps.x}px`,
            top: `${contextMenuProps.y}px`,
            zIndex: 9999
          }}
        >
          {contextMenuProps.items.map((contextItem) => (
            <div 
              key={contextItem.id}
              className={cn(
                "windows-context-menu-item",
                contextItem.disabled && "opacity-50 pointer-events-none"
              )}
              onClick={() => {
                if (!contextItem.disabled && contextItem.onClick) {
                  contextItem.onClick();
                  contextMenuProps.onClose();
                }
              }}
            >
              {contextItem.icon && <span className="w-4 h-4">{contextItem.icon}</span>}
              <span className="flex-1">{contextItem.label}</span>
              {contextItem.shortcut && (
                <span className="text-gray-500 text-xs ml-4">{contextItem.shortcut}</span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
