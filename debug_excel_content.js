import XLSX from 'xlsx';
import fs from 'fs';

// Excel dosyasını oku
const workbook = XLSX.readFile('/Users/<USER>/eits/hukumlu-listesi-2025-06-05.xlsx');
const sheetName = workbook.SheetNames[0];
const worksheet = workbook.Sheets[sheetName];

// JSON'a çevir
const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

console.log('Sheet Name:', sheetName);
console.log('Total Rows:', jsonData.length);

// İlk birkaç satırı göster
console.log('\n=== İLK 10 SATIR ===');
jsonData.slice(0, 10).forEach((row, index) => {
  console.log(`Satır ${index + 1}:`, row);
});

if (jsonData.length > 0) {
  const headers = jsonData[0];
  console.log('\n=== BAŞLIKLAR ===');
  headers.forEach((header, index) => {
    console.log(`Kolon ${index + 1}: "${header}"`);
  });

  // Veri satırlarını kontrol et
  const dataRows = jsonData.slice(1);
  
  console.log('\n=== VERİ ANALİZİ ===');
  console.log('Toplam veri satırı:', dataRows.length);
  
  let convictCount = 0;
  let signaturePeriodCount = 0;
  let exemptionCount = 0;
  
  const tcNumbers = new Set();
  
  dataRows.forEach((row, index) => {
    const rowNumber = index + 2; // +2 çünkü 1 başlık + 0-based index
    
    if (row.some(cell => cell !== undefined && cell !== '')) {
      const tcNo = row[0]; // İlk kolon TC No olmalı
      
      if (tcNo) {
        tcNumbers.add(tcNo);
        convictCount++;
        
        // İmza periyodu kontrolü (sütun 11-15 arası)
        const hasSignaturePeriod = row.slice(11, 16).some(cell => cell !== undefined && cell !== '');
        if (hasSignaturePeriod) {
          signaturePeriodCount++;
          console.log(`Satır ${rowNumber}: İmza periyodu verisi var - ${row.slice(11, 16)}`);
        }
        
        // Muafiyet kontrolü (sütun 19-23 arası)
        const hasExemption = row.slice(19, 24).some(cell => cell !== undefined && cell !== '');
        if (hasExemption) {
          exemptionCount++;
          console.log(`Satır ${rowNumber}: Muafiyet verisi var - ${row.slice(19, 24)}`);
        }
        
        console.log(`Satır ${rowNumber} - TC: ${tcNo}, İmza: ${hasSignaturePeriod ? 'Var' : 'Yok'}, Muafiyet: ${hasExemption ? 'Var' : 'Yok'}`);
      }
    }
  });
  
  console.log('\n=== ÖZET ===');
  console.log('Benzersiz TC kimlik numarası sayısı:', tcNumbers.size);
  console.log('Toplam hükümlü satırı:', convictCount);
  console.log('İmza periyodu verisi olan satır sayısı:', signaturePeriodCount);
  console.log('Muafiyet verisi olan satır sayısı:', exemptionCount);
  
  console.log('\n=== TC NUMARALARI ===');
  Array.from(tcNumbers).forEach(tc => console.log(tc));
}

// Detaylı satır analizi
console.log('\n=== DETAYLI SATIR ANALİZİ ===');
const dataRows = jsonData.slice(1);
dataRows.forEach((row, index) => {
  const rowNumber = index + 2;
  if (row.some(cell => cell !== undefined && cell !== '')) {
    console.log(`\nSatır ${rowNumber}:`);
    console.log('  TC No:', row[0]);
    console.log('  Ad:', row[1]);
    console.log('  Soyad:', row[2]);
    console.log('  İmza Periyodu Başlangıç:', row[11]);
    console.log('  İmza Periyodu Bitiş:', row[12]);
    console.log('  İmza Sıklığı Türü:', row[13]);
    console.log('  İmza Sıklığı Değeri:', row[14]);
    console.log('  Muafiyet Türü:', row[19]);
    console.log('  Muafiyet Başlangıç:', row[20]);
    console.log('  Muafiyet Bitiş:', row[21]);
  }
});
