CREATE TABLE `exemptions` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`convict_id` integer NOT NULL,
	`exemption_type` text NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text NOT NULL,
	`description` text,
	`document_path` text,
	`created_by` integer,
	`is_active` integer DEFAULT true NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP,
	FOREIGN KEY (`convict_id`) REFERENCES `convicts`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
