import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import type { InsertExemption } from '@shared/schema';

const exemptionSchema = z.object({
  exemptionType: z.enum(['LEAVE', 'MEDICAL_REPORT'], {
    required_error: 'Muafiyet türü seçiniz',
  }),
  startDate: z.string().min(1, 'Başlangıç tarihi gereklidir'),
  endDate: z.string().min(1, 'Bitiş tarihi gereklidir'),
  description: z.string().optional(),
  documentPath: z.string().optional(),
  isActive: z.boolean().default(true),
}).refine(
  (data) => new Date(data.endDate) >= new Date(data.startDate),
  {
    message: 'Bitiş tarihi başlangıç tarihinden önce olamaz',
    path: ['endDate'],
  }
);

type ExemptionFormData = z.infer<typeof exemptionSchema>;

interface ExemptionFormProps {
  initialData?: Partial<ExemptionFormData>;
  onSubmit: (data: InsertExemption) => void;
  onCancel: () => void;
  isLoading?: boolean;
  submitLabel?: string;
}

export default function ExemptionForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = 'Kaydet',
}: ExemptionFormProps) {
  const form = useForm<ExemptionFormData>({
    resolver: zodResolver(exemptionSchema),
    defaultValues: {
      exemptionType: initialData?.exemptionType || 'LEAVE',
      startDate: initialData?.startDate || '',
      endDate: initialData?.endDate || '',
      description: initialData?.description || '',
      documentPath: initialData?.documentPath || '',
      isActive: initialData?.isActive ?? true,
    },
  });

  const handleSubmit = (data: ExemptionFormData) => {
    const exemptionData: InsertExemption = {
      convict_id: 0, // This will be set by the parent component
      exemption_type: data.exemptionType,
      start_date: data.startDate,
      end_date: data.endDate,
      description: data.description || undefined,
      document_path: data.documentPath || undefined,
      is_active: data.isActive,
    };
    onSubmit(exemptionData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Exemption Type */}
          <FormField
            control={form.control}
            name="exemptionType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Muafiyet Türü *</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="modern-input">
                      <SelectValue placeholder="Muafiyet türü seçiniz" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="LEAVE">İzin</SelectItem>
                    <SelectItem value="MEDICAL_REPORT">Doktor Raporu</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Active Status */}
          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <FormLabel>Aktif Durum</FormLabel>
                  <div className="text-sm text-gray-600">
                    Muafiyetin aktif olup olmadığını belirler
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Start Date */}
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Başlangıç Tarihi *</FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    {...field}
                    className="modern-input"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* End Date */}
          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bitiş Tarihi *</FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    {...field}
                    className="modern-input"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Açıklama</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Muafiyet ile ilgili detaylar..."
                  {...field}
                  className="modern-input resize-none"
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Document Path */}
        <FormField
          control={form.control}
          name="documentPath"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Belge Yolu</FormLabel>
              <FormControl>
                <Input
                  placeholder="Belge dosya yolu (opsiyonel)"
                  {...field}
                  className="modern-input"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Form Actions */}
        <div className="flex items-center space-x-4 pt-4">
          <Button
            type="submit"
            disabled={isLoading}
            className="windows-button-primary"
          >
            {isLoading ? 'Kaydediliyor...' : submitLabel}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="windows-button"
          >
            İptal
          </Button>
        </div>
      </form>
    </Form>
  );
}
