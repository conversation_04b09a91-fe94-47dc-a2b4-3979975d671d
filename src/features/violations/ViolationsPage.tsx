import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getViolationsForLast30Days } from '@/lib/tauri-api';
import type { ViolationRecord } from '@shared/schema';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ExclamationTriangleIcon, EyeIcon } from '@heroicons/react/24/outline';
import DataTable from '@/components/common/DataTable';
import { formatDate } from '@/lib/utils';
import { Link } from 'wouter';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Tarih hesaplama fonksiyonları
const getDateRange = () => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 30);
  return {
    startDate,
    endDate
  };
};

// DataTable'ın beklediği Column yapısına uygun bir arayüz
interface ViolationsColumn {
  key: keyof ViolationRecord | string;
  label: string;
  render?: (item: ViolationRecord) => React.ReactNode;
}

const columns: ViolationsColumn[] = [
  {
    key: 'tc_no',
    label: 'TC Kimlik No',
  },
  {
    key: 'first_name',
    label: 'Adı',
  },
  {
    key: 'last_name',
    label: 'Soyadı',
  },
  {
    key: 'violation_date',
    label: 'İhlal Tarihi',
    render: (item: ViolationRecord) => (
      <Badge className="bg-red-100 text-red-800 border-red-200 text-xs px-2 py-1 rounded">
        {formatDate(item.violation_date)}
      </Badge>
    ),
  },
  {
    key: 'expected_frequency',
    label: 'O Gün Beklenen Sıklık',
  },
  {
    key: 'actions',
    label: 'İşlemler',
    render: (item: ViolationRecord) => (
      <div className="flex gap-1">
        <Link href={`/reports/convict-history?convictId=${item.convict_id}`}>
          <Button variant="windows" size="sm" className="h-7 w-7 p-0" title="İmza Geçmişini Gör">
            <EyeIcon className="w-3 h-3" />
          </Button>
        </Link>
      </div>
    ),
  },
];

export default function ViolationsPage() {
  const { data: violations, isLoading, error } = useQuery({
    queryKey: ['violations-last-30-days'],
    queryFn: getViolationsForLast30Days,
    staleTime: 60000,
  });

  const { startDate, endDate } = getDateRange();

  if (isLoading) {
    return (
      <div className="windows-content flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-3 text-gray-600 text-sm">İhlaller yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="windows-content flex items-center justify-center">
        <div className="max-w-md p-4">
          <Alert variant="destructive" className="bg-red-50 border-red-200">
            <AlertDescription className="text-red-800 text-sm">
              İhlaller yüklenirken bir hata oluştu: {error.message}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="windows-content">
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="windows-title">
              Tüm İhlaller (Son 30 Gün)
            </h1>
            <p className="windows-subtitle">
              {formatDate(startDate)} - {formatDate(endDate)} tarihleri arasındaki tüm ihlaller listelenmektedir.
            </p>
            {violations && (
              <div className="mt-2 flex items-center gap-4 text-xs text-gray-600">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  <span>İhlal Sayısı: {violations.length}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="p-4">
        <Card className="windows-card">
          <CardHeader>
            <CardTitle className="windows-section-title flex items-center">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
              İhlal Listesi
            </CardTitle>
          </CardHeader>
          <CardContent>
            {violations && violations.length > 0 ? (
              <DataTable columns={columns} data={violations} />
            ) : (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <ExclamationTriangleIcon className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-sm font-semibold text-gray-900 mb-1">İhlal Bulunmamaktadır</h3>
                <p className="text-xs text-gray-600">Son 30 gün içinde herhangi bir imza ihlali bulunmamaktadır.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 