import { useState, useEffect, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useSignatureStore } from '@/store/signatureStore';
import { getCurrentDate, getCurrentTime } from '@/lib/utils';
import { 
  searchConvictByTcNo, 
  searchConvictsByName, 
  createSignature,
  getConvictSignatures,
  getConvictById,
  getActiveExemptionsForDate,
  getConvictSignaturePeriods
} from '@/lib/tauri-api';
import { validateSignatureTime, getTimeRestrictionText } from '@/lib/signature-dates';
import {
  MagnifyingGlassIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  DocumentTextIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import type { Convict, Signature } from '@shared/schema';

export default function RecordSignaturePage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const {
    searchQuery,
    foundConvict,
    signatureStatus,
    setSearchQuery,
    setFoundConvict,
    setSignatureStatus,
    clearSearch: storeClearSearch, 
  } = useSignatureStore();

  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingConvict, setIsLoadingConvict] = useState(false);

  const stableToast = useCallback(toast, [toast]);

  // Helper function to validate signature time restrictions
  const validateCurrentSignatureTime = async (convictId: number) => {
    try {
      const periods = await getConvictSignaturePeriods(convictId);
      const activePeriods = periods.filter(p => p.is_active);
      
      if (activePeriods.length === 0) {
        return {
          isValid: true,
          errors: [],
          activePeriods: []
        };
      }

      const currentTime = getCurrentTime();
      const currentDate = getCurrentDate();
      let allErrors: string[] = [];
      let hasValidPeriod = false;
      
      const periodInfo = activePeriods.map(period => {
        const validation = validateSignatureTime(period, currentTime, currentDate);
        if (validation.isValid) {
          hasValidPeriod = true;
        } else {
          allErrors.push(...validation.errors);
        }
        
        return {
          id: period.id,
          timeRestriction: getTimeRestrictionText(period)
        };
      });

      return {
        isValid: hasValidPeriod,
        errors: hasValidPeriod ? [] : allErrors,
        activePeriods: periodInfo
      };
    } catch (error) {
      console.error('Error validating signature time:', error);
      return {
        isValid: true, // Allow signature if validation fails
        errors: [],
        activePeriods: []
      };
    }
  };

  const loadConvictById = useCallback(async (id: number) => {
    setIsLoadingConvict(true);
    try {
      const convict = await getConvictById(id);
      if (convict) {
        setFoundConvict(convict);
        setSearchQuery(`${convict.first_name} ${convict.last_name}`);
        const today = getCurrentDate();
        const isRequired = convict.is_active;
        try {
          const signatures = await getConvictSignatures(convict.id);
          const alreadySigned = signatures.some(
            (sig: Signature) => sig.signature_date === today
          );
          const todaysSignature = signatures.find(
            (sig: Signature) => sig.signature_date === today
          );
          
          // Check for active exemptions
          const activeExemptions = await getActiveExemptionsForDate(convict.id, today);
          const hasActiveExemptions = activeExemptions.length > 0;
          const exemptions = activeExemptions.map(exemption => ({
            type: exemption.exemption_type,
            description: exemption.description || undefined,
          }));

          // Check time validation for signature periods
          const timeValidation = await validateCurrentSignatureTime(convict.id);
          
          setSignatureStatus({
            isRequired: isRequired && !hasActiveExemptions,
            alreadySigned,
            signatureTime: todaysSignature?.signature_time,
            hasActiveExemptions,
            exemptions,
            timeValidation,
          });
        } catch (sigError) {
          console.error("Error fetching signatures for convict:", sigError);
          setSignatureStatus({ 
            isRequired, 
            alreadySigned: false,
            hasActiveExemptions: false,
            exemptions: [],
            timeValidation: undefined,
          });
          stableToast({
            title: 'İmza Durumu Hatası',
            description: 'Hükümlü imza durumu getirilirken bir hata oluştu.',
            variant: 'destructive',
          });
        }
      } else {
        setFoundConvict(null);
        stableToast({
          title: 'Hükümlü Bulunamadı',
          description: "URL'de belirtilen ID ile hükümlü bulunamadı.",
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error("Error loading convict by ID:", error);
      setFoundConvict(null);
      stableToast({
        title: 'Hata',
        description: 'Hükümlü bilgileri yüklenirken bir hata oluştu.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingConvict(false);
    }
  }, [setFoundConvict, setSearchQuery, setSignatureStatus, stableToast]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const convictIdFromUrl = params.get('convictId');
    if (convictIdFromUrl) {
      const numericConvictId = parseInt(convictIdFromUrl, 10);
      if (!isNaN(numericConvictId)) {
         loadConvictById(numericConvictId);
      }
    }
  }, [loadConvictById]);

  const recordSignatureMutation = useMutation({
    mutationFn: async () => {
      if (!foundConvict) throw new Error('Hükümlü seçilmedi');
      const today = getCurrentDate();
      const currentTime = getCurrentTime();
      return await createSignature({
        convict_id: foundConvict.id,
        signature_date: today,
        signature_time: currentTime,
        recorded_by: undefined, // TODO: Get from auth context
      });
    },
    onSuccess: () => {
      stableToast({
        title: 'İmza Kaydedildi',
        description: 'İmza başarıyla sisteme kaydedildi.',
      });
      setSignatureStatus({
        isRequired: signatureStatus.isRequired,
        alreadySigned: true,
        signatureTime: getCurrentTime(),
        hasActiveExemptions: signatureStatus.hasActiveExemptions,
        exemptions: signatureStatus.exemptions,
        timeValidation: signatureStatus.timeValidation,
      });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      queryClient.invalidateQueries({ queryKey: ['expected-signatures-today'] });
    },
    onError: (error: Error) => {
      stableToast({
        title: 'Hata',
        description: error.message || 'İmza kaydedilirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      stableToast({ title: 'Uyarı', description: 'Lütfen TC Kimlik No veya Ad Soyad giriniz.', variant: 'destructive' });
      return;
    }
    setIsSearching(true);
    try {
      let convict: Convict | null = null;
      if (/^\d{11}$/.test(searchQuery.trim())) {
        convict = await searchConvictByTcNo(searchQuery.trim());
      } else {
        const nameParts = searchQuery.trim().split(' ').filter(part => part.length > 0);
        const firstName = nameParts[0] || "";
        const lastName = nameParts.slice(1).join(' ') || "";
        if (!firstName && !lastName) {
            stableToast({ title: 'Uyarı', description: 'Lütfen geçerli bir Ad Soyad giriniz.', variant: 'destructive' });
            setIsSearching(false);
            return;
        }
        const convicts = await searchConvictsByName(firstName, lastName);
        if (convicts.length > 0) convict = convicts[0];
      }

      if (convict) {
        setFoundConvict(convict);
        const today = getCurrentDate();
        const isRequired = convict.is_active;
        try {
          const signatures = await getConvictSignatures(convict.id);
          const alreadySigned = signatures.some(sig => sig.signature_date === today);
          const todaysSignature = signatures.find(sig => sig.signature_date === today);
          
          // Check for active exemptions
          const activeExemptions = await getActiveExemptionsForDate(convict.id, today);
          const hasActiveExemptions = activeExemptions.length > 0;
          const exemptions = activeExemptions.map(exemption => ({
            type: exemption.exemption_type,
            description: exemption.description || undefined,
          }));

          // Check time validation for signature periods
          const timeValidation = await validateCurrentSignatureTime(convict.id);
          
          setSignatureStatus({ 
            isRequired: isRequired && !hasActiveExemptions, 
            alreadySigned, 
            signatureTime: todaysSignature?.signature_time,
            hasActiveExemptions,
            exemptions,
            timeValidation,
          });
        } catch {
          setSignatureStatus({ 
            isRequired, 
            alreadySigned: false,
            hasActiveExemptions: false,
            exemptions: [],
            timeValidation: undefined,
          });
        }
      } else {
        setFoundConvict(null);
        stableToast({ title: 'Bulunamadı', description: 'Girdiğiniz bilgilere uygun hükümlü bulunamadı.', variant: 'destructive' });
      }
    } catch (error) {
      console.error('Search error:', error);
      stableToast({ title: 'Hata', description: 'Arama yapılırken bir hata oluştu.', variant: 'destructive' });
      setFoundConvict(null);
    } finally {
      setIsSearching(false);
    }
  };

  const handleRecordSignature = () => {
    if (isLoadingConvict) {
      stableToast({ title: 'Lütfen Bekleyin', description: 'Hükümlü bilgileri yükleniyor...' });
      return;
    }
    if (!foundConvict) {
      stableToast({ title: 'Hükümlü Seçilmedi', description: 'Lütfen önce bir hükümlü arayın veya seçin.', variant: 'destructive' });
      return;
    }
    
    // Check time validation before recording signature
    if (signatureStatus.timeValidation && !signatureStatus.timeValidation.isValid) {
      stableToast({ 
        title: 'İmza Saati Uygun Değil', 
        description: 'Bu hükümlü şu anda imza atamaz. Lütfen belirtilen saatlerde tekrar deneyin.', 
        variant: 'destructive' 
      });
      return;
    }
    
    recordSignatureMutation.mutate();
  };
  
  const localClearSearch = () => {
    storeClearSearch(); 
    setSearchQuery('');
    window.history.replaceState({}, document.title, "/signatures/record"); 
  };

  const renderSignatureStatus = () => {
    if (!foundConvict && !isLoadingConvict) return null;
    if (isLoadingConvict) return <p className="text-center text-gray-500">İmza durumu yükleniyor...</p>;
    if (!signatureStatus) return <p className="text-center text-gray-500">İmza durumu belirlenemedi.</p>;

    if (signatureStatus.hasActiveExemptions) {
      const exemptionTypeLabels = {
        LEAVE: 'İzin',
        MEDICAL_REPORT: 'Doktor Raporu'
      };
      
      return (
        <Alert className="bg-orange-50 border-orange-200">
          <DocumentTextIcon className="h-5 w-5 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <p className="font-medium">Muafiyet durumu aktif</p>
            <div className="mt-2 space-y-1">
              {signatureStatus.exemptions?.map((exemption, index) => (
                <div key={index} className="text-sm">
                  <span className="font-medium">
                    {exemptionTypeLabels[exemption.type as keyof typeof exemptionTypeLabels] || exemption.type}
                  </span>
                  {exemption.description && (
                    <span className="text-orange-700"> - {exemption.description}</span>
                  )}
                </div>
              ))}
            </div>
            <p className="text-sm mt-2">Bu hükümlü bugün imza atmak zorunda değil.</p>
          </AlertDescription>
        </Alert>
      );
    }

    if (signatureStatus.alreadySigned) {
      return (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircleIcon className="h-5 w-5 text-green-600" />
          <AlertDescription className="text-green-800">
            <p className="font-medium">Bugün için imza zaten alındı</p>
            {signatureStatus.signatureTime && <p>İmza saati: {signatureStatus.signatureTime}</p>}
          </AlertDescription>
        </Alert>
      );
    }
    if (signatureStatus.isRequired) {
      // Check if there are time validation issues
      if (signatureStatus.timeValidation && !signatureStatus.timeValidation.isValid) {
        return (
          <div className="space-y-3">
            <Alert className="bg-red-50 border-red-200">
              <ClockIcon className="h-5 w-5 text-red-600" />
              <AlertDescription className="text-red-800">
                <p className="font-medium">İmza saati kısıtlaması</p>
                <div className="mt-2 space-y-1">
                  {signatureStatus.timeValidation.errors.map((error, index) => (
                    <p key={index} className="text-sm">{error}</p>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
            
            {signatureStatus.timeValidation.activePeriods && signatureStatus.timeValidation.activePeriods.length > 0 && (
              <Alert className="bg-blue-50 border-blue-200">
                <InformationCircleIcon className="h-5 w-5 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  <p className="font-medium">İmza saatleri</p>
                  <div className="mt-2 space-y-1">
                    {signatureStatus.timeValidation.activePeriods.map((period, index) => (
                      <p key={index} className="text-sm">{period.timeRestriction}</p>
                    ))}
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        );
      }
      
      return (
        <div className="space-y-3">
          <Alert className="bg-green-50 border-green-200">
            <CheckCircleIcon className="h-5 w-5 text-green-600" />
            <AlertDescription className="text-green-800">
              <p className="font-medium">İmza alınabilir</p>
              <p>Bu hükümlü şu anda imza atabilir.</p>
            </AlertDescription>
          </Alert>
          
          {signatureStatus.timeValidation?.activePeriods && signatureStatus.timeValidation.activePeriods.length > 0 && (
            <Alert className="bg-blue-50 border-blue-200">
              <InformationCircleIcon className="h-5 w-5 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <p className="font-medium">İmza saatleri</p>
                <div className="mt-2 space-y-1">
                  {signatureStatus.timeValidation.activePeriods.map((period, index) => (
                    <p key={index} className="text-sm">{period.timeRestriction}</p>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>
      );
    }
    return (
      <Alert className="bg-gray-50 border-gray-200">
        <InformationCircleIcon className="h-5 w-5 text-gray-600" />
        <AlertDescription className="text-gray-800">
          <p className="font-medium">Bugün imza günü değil</p>
          <p>Bu hükümlü bugün imza atmak zorunda değil.</p>
        </AlertDescription>
      </Alert>
    );
  };

  return (
    <div className="windows-content">
      {/* Windows Toolbar */}
      <div className="windows-toolbar-secondary">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="windows-title">İmza Kaydı</h1>
            <span className="text-gray-600 text-xs ml-2">
              {isLoadingConvict ? "- Hükümlü bilgileri yükleniyor..." : "- Hükümlü imza kayıtlarını işle"}
            </span>
          </div>
        </div>
      </div>

      <div className="p-3">
        {/* Search Card */}
        <div className="windows-card mb-3">
          <div className="windows-section-title">Hükümlü Arama</div>
          <div className="p-3">
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex-1">
                <Label htmlFor="search" className="windows-label">TC Kimlik No veya Ad Soyad</Label>
                <Input
                  id="search"
                  placeholder="TC Kimlik No veya Ad Soyad ile ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  disabled={isSearching || isLoadingConvict}
                  className="windows-input"
                />
              </div>
              <div className="flex items-end gap-2">
                <Button 
                  onClick={handleSearch} 
                  disabled={isSearching || isLoadingConvict}
                  className="windows-button-primary"
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-1" />
                  {isSearching ? 'Aranıyor...' : (isLoadingConvict ? 'Yükleniyor...': 'Ara')}
                </Button>
                {foundConvict && (
                  <Button 
                    onClick={localClearSearch} 
                    disabled={isLoadingConvict}
                    className="windows-button"
                  >
                    Temizle
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Convict Details Card */}
        {(foundConvict || isLoadingConvict) && (
          <div className="windows-card mb-3">
            <div className="windows-section-title">
              {isLoadingConvict && "Hükümlü Yükleniyor..."}
              {foundConvict && !isLoadingConvict && `${foundConvict.first_name} ${foundConvict.last_name}`}
            </div>
            {foundConvict && !isLoadingConvict && (
              <div className="p-3">
                <div className="space-y-2 mb-3">
                  <p className="text-xs text-gray-600">TC Kimlik No: {foundConvict.tc_no}</p>
                  <p className="text-xs text-gray-600">
                    Denetim Süresi: {foundConvict.supervision_start_date} - {foundConvict.supervision_end_date}
                  </p>
                  {foundConvict.phone_number && (
                    <p className="text-xs text-gray-600">Telefon: {foundConvict.phone_number}</p>
                  )}
                  {foundConvict.relative_phone_number && (
                    <p className="text-xs text-gray-600">Yakını Telefon: {foundConvict.relative_phone_number}</p>
                  )}
                  {foundConvict.address && (
                    <p className="text-xs text-gray-600">Adres: {foundConvict.address}</p>
                  )}
                  <span className={`inline-block px-2 py-1 text-xs rounded ${
                    foundConvict.is_active 
                      ? 'bg-green-100 text-green-800 border border-green-200' 
                      : 'bg-gray-100 text-gray-800 border border-gray-200'
                  }`}>
                    {foundConvict.is_active ? 'Aktif' : 'Pasif'}
                  </span>
                </div>
                {renderSignatureStatus()}
              </div>
            )}
            {isLoadingConvict && (
              <div className="p-3">
                <p className="text-center text-gray-500 py-4 text-xs">Hükümlü bilgileri yükleniyor...</p>
              </div>
            )}
          </div>
        )}

        {/* Record Signature Card */}
        {foundConvict && !isLoadingConvict && signatureStatus && signatureStatus.isRequired && !signatureStatus.alreadySigned && !signatureStatus.hasActiveExemptions && (
          <div className="windows-card">
            <div className="windows-section-title">İmzayı Kaydet</div>
            <div className="p-3">
              <p className="text-xs text-gray-600 mb-3">
                {foundConvict.first_name} {foundConvict.last_name} için bugünün imzasını kaydedin.
              </p>
              <Button 
                onClick={handleRecordSignature} 
                disabled={
                  recordSignatureMutation.isPending || 
                  isLoadingConvict ||
                  (signatureStatus.timeValidation && !signatureStatus.timeValidation.isValid)
                }
                className={`w-full ${
                  signatureStatus.timeValidation && !signatureStatus.timeValidation.isValid
                    ? 'windows-button-disabled'
                    : 'windows-button-primary'
                }`}
              >
                {recordSignatureMutation.isPending 
                  ? 'Kaydediliyor...' 
                  : signatureStatus.timeValidation && !signatureStatus.timeValidation.isValid
                    ? 'İmza Saati Uygun Değil'
                    : 'İmzayı Kaydet'
                }
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
