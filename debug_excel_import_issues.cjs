const XLSX = require('xlsx');

// Debug script to identify Excel import parsing issues
function debugExcelImport() {
    console.log('=== Excel Import Debug Analysis ===\n');
    
    try {
        // Read the Excel file
        const workbook = XLSX.readFile('hukumlu-listesi-2025-06-05.xlsx');
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        console.log('1. File Structure:');
        console.log('   Headers:', jsonData[0]);
        console.log('   Total rows:', jsonData.length);
        console.log('   Data rows:', jsonData.length - 1);
        
        // Convert to object format like the component does
        const headers = jsonData[0];
        const dataRows = jsonData.slice(1);
        
        console.log('\n2. Parsing Each Row:');
        
        dataRows.forEach((row, index) => {
            if (row.some(cell => cell !== undefined && cell !== '')) {
                console.log(`\n--- Row ${index + 2} ---`);
                
                const rowData = {};
                headers.forEach((header, headerIndex) => {
                    rowData[header] = row[headerIndex];
                });
                
                // Test signature period validation logic
                console.log('Signature Period Data:');
                console.log('  TC No:', rowData['TC Kimlik No']);
                console.log('  Start Date:', rowData['İmza Periyodu Başlangıç']);
                console.log('  End Date:', rowData['İmza Periyodu Bitiş']);
                console.log('  Frequency Type:', rowData['İmza Sıklığı Türü']);
                console.log('  Frequency Value:', rowData['İmza Sıklığı Değeri']);
                
                // Test the frequency type mapping
                const frequencyTypeMapping = {
                    'HAFTALIK': 'WEEKLY',
                    'WEEKLY': 'WEEKLY',
                    'GÜNLÜK': 'X_DAYS',
                    'GUNLUK': 'X_DAYS',
                    'X_DAYS': 'X_DAYS',
                    'AYLIK': 'MONTHLY_SPECIFIC',
                    'MONTHLY_SPECIFIC': 'MONTHLY_SPECIFIC',
                    'AYLIK BELİRLİ GÜN': 'MONTHLY_SPECIFIC',
                    'AYLIK BELIRLI GUN': 'MONTHLY_SPECIFIC'
                };
                
                const weekdayMapping = {
                    'PAZARTESİ': 'MONDAY',
                    'PAZARTESI': 'MONDAY',
                    'SALI': 'TUESDAY',
                    'ÇARŞAMBA': 'WEDNESDAY',
                    'CARSAMBA': 'WEDNESDAY',
                    'PERŞEMBE': 'THURSDAY',
                    'PERSEMBE': 'THURSDAY',
                    'CUMA': 'FRIDAY',
                    'CUMARTESİ': 'SATURDAY',
                    'CUMARTESI': 'SATURDAY',
                    'PAZAR': 'SUNDAY'
                };
                
                const rawFrequencyType = String(rowData['İmza Sıklığı Türü'] || '').trim().toUpperCase();
                const mappedFrequencyType = frequencyTypeMapping[rawFrequencyType] || rawFrequencyType;
                
                let rawFrequencyValue = String(rowData['İmza Sıklığı Değeri'] || '').trim().toUpperCase();
                let processedFrequencyValue = rawFrequencyValue;
                
                if (mappedFrequencyType === 'WEEKLY') {
                    if (rawFrequencyValue.includes(',')) {
                        // Multiple days
                        const days = rawFrequencyValue.split(',').map(d => d.trim().toUpperCase());
                        const mappedDays = days.map(day => weekdayMapping[day] || day);
                        processedFrequencyValue = mappedDays.join(',');
                        
                        console.log('  Multiple Days Processing:');
                        console.log('    Raw days:', days);
                        console.log('    Mapped days:', mappedDays);
                        console.log('    Final value:', processedFrequencyValue);
                        
                        // Validate each day
                        const validWeekdays = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];
                        let allDaysValid = true;
                        const invalidDays = [];
                        
                        for (const day of mappedDays) {
                            if (day && !validWeekdays.includes(day)) {
                                allDaysValid = false;
                                invalidDays.push(day);
                            }
                        }
                        
                        if (!allDaysValid) {
                            console.log('  VALIDATION ERROR: Invalid days found:', invalidDays);
                        } else {
                            console.log('  VALIDATION: All days are valid');
                        }
                    } else {
                        // Single day
                        processedFrequencyValue = weekdayMapping[rawFrequencyValue] || rawFrequencyValue;
                        console.log('  Single Day Processing:');
                        console.log('    Raw day:', rawFrequencyValue);
                        console.log('    Mapped day:', processedFrequencyValue);
                    }
                }
                
                console.log('  Final Processing Result:');
                console.log('    Frequency Type: {} -> {}'.replace('{}', rawFrequencyType).replace('{}', mappedFrequencyType));
                console.log('    Frequency Value: {} -> {}'.replace('{}', rawFrequencyValue).replace('{}', processedFrequencyValue));
                
                // Check if signature period data exists
                const hasStartDate = rowData['İmza Periyodu Başlangıç'] !== undefined && rowData['İmza Periyodu Başlangıç'] !== '';
                const hasEndDate = rowData['İmza Periyodu Bitiş'] !== undefined && rowData['İmza Periyodu Bitiş'] !== '';
                const hasFrequencyType = rowData['İmza Sıklığı Türü'] !== undefined && rowData['İmza Sıklığı Türü'] !== '';
                const hasFrequencyValue = rowData['İmza Sıklığı Değeri'] !== undefined && rowData['İmza Sıklığı Değeri'] !== '';
                
                const hasSignaturePeriodData = hasStartDate || hasEndDate || hasFrequencyType || hasFrequencyValue;
                
                console.log('  Data Completeness Check:');
                console.log('    Has Start Date:', hasStartDate);
                console.log('    Has End Date:', hasEndDate);
                console.log('    Has Frequency Type:', hasFrequencyType);
                console.log('    Has Frequency Value:', hasFrequencyValue);
                console.log('    Should Process:', hasSignaturePeriodData);
                
                // Test exemption data
                console.log('\nExemption Data:');
                console.log('  Exemption Type:', rowData['Muafiyet Türü']);
                console.log('  Start Date:', rowData['Muafiyet Başlangıç']);
                console.log('  End Date:', rowData['Muafiyet Bitiş']);
                
                const exemptionTypeMapping = {
                    'İZİN': 'LEAVE',
                    'IZIN': 'LEAVE',
                    'LEAVE': 'LEAVE',
                    'SAĞLIK RAPORU': 'MEDICAL_REPORT',
                    'SAGLIK RAPORU': 'MEDICAL_REPORT',
                    'MEDICAL_REPORT': 'MEDICAL_REPORT',
                    'SAĞLIK': 'MEDICAL_REPORT',
                    'SAGLIK': 'MEDICAL_REPORT'
                };
                
                const rawExemptionType = String(rowData['Muafiyet Türü'] || '').trim().toUpperCase();
                const mappedExemptionType = exemptionTypeMapping[rawExemptionType] || rawExemptionType;
                
                if (rawExemptionType) {
                    console.log('  Exemption Type Mapping: {} -> {}'.replace('{}', rawExemptionType).replace('{}', mappedExemptionType));
                }
                
                const hasExemptionData = rowData['Muafiyet Türü'] || rowData['Muafiyet Başlangıç'] || rowData['Muafiyet Bitiş'];
                console.log('  Should Process Exemption:', !!hasExemptionData);
            }
        });
        
        console.log('\n=== Summary ===');
        console.log('Excel file structure appears correct');
        console.log('Headers match expected format');
        console.log('Day name mappings are working');
        console.log('Main issue might be in validation logic or data processing');
        
    } catch (error) {
        console.error('Error analyzing Excel file:', error);
    }
}

debugExcelImport();
