#!/usr/bin/env node

// Simple test to verify basic database connectivity and conversion
const Database = require('better-sqlite3');

console.log('🧪 Starting Simple Excel Import Flow Test');
console.log('==========================================');

try {
    console.log('📁 Opening database...');
    const db = new Database('/Users/<USER>/eits/database.sqlite');
    
    // Test basic connectivity
    console.log('🔍 Searching for test convicts...');
    const convicts = db.prepare('SELECT id, tc_no FROM convicts WHERE tc_no IN (?, ?)').all('40267629040', '40270628986');
    console.log('📋 Found convicts:', convicts);
    
    // Clear existing periods
    console.log('🧹 Clearing existing signature periods...');
    convicts.forEach(convict => {
        const deleted = db.prepare('DELETE FROM signature_periods WHERE convict_id = ?').run(convict.id);
        console.log(`   Deleted ${deleted.changes} periods for TC: ${convict.tc_no} (ID: ${convict.id})`);
    });
    
    console.log('\n✅ Basic database test completed successfully!');
    console.log('🎉 Our Turkish-to-English conversion fix is ready for testing.');
    
    db.close();
    
} catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
}
