# MANUAL TESTING CHECKLIST - Contact Fields

**Application URL:** http://localhost:1420

## 🧪 TEST EXECUTION CHECKLIST

### Test 1: Form Input ✅ / ❌
- [ ] Navigate to "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" (Add Convict)
- [ ] Form displays contact fields:
  - [ ] "Hükümlü Telefon Numarası" (tel input)
  - [ ] "Yakınına Ait Telefon Numarası" (tel input)  
  - [ ] "Adres Bilgileri" (textarea)
- [ ] Fill required fields + contact info
- [ ] Submit form successfully
- [ ] Verify data saved in database

**Test Data:**
- TC: 11111111111
- Ad: TestManual  
- Soyad: User
- Phone: +90 ************
- Relative Phone: +90 ************
- Address: Test Mahallesi, Test Sokak No:1, Test/Şehir

### Test 2: Excel Import ✅ / ❌
- [ ] Navigate to import section
- [ ] Select file: `test_contact_fields.xlsx`
- [ ] Preview shows Turkish headers:
  - [ ] "<PERSON><PERSON>kümlü Telefon Numarası"
  - [ ] "Yakınına Ait Telefon Numarası"
  - [ ] "Adres Bilgileri"
- [ ] Import executes successfully
- [ ] Check data appears in convict list

### Test 3: Excel Export ✅ / ❌
- [ ] Navigate to export section
- [ ] Export convicts data
- [ ] Open downloaded Excel file
- [ ] Verify columns exist:
  - [ ] Contact field headers in Turkish
  - [ ] Data populated for existing records
  - [ ] Column widths appropriate

### Test 4: Edit Convict ✅ / ❌
- [ ] Select existing convict from list
- [ ] Click edit/modify button
- [ ] Contact fields pre-populated if data exists
- [ ] Modify contact information
- [ ] Save changes
- [ ] Verify changes persist

### Test 5: Signature Recording ✅ / ❌
- [ ] Navigate to signature recording
- [ ] Select convict with contact info
- [ ] Verify contact information displays:
  - [ ] Phone number shown
  - [ ] Relative phone shown
  - [ ] Address displayed
- [ ] Test with convict without contact info
- [ ] Verify conditional display works

### Test 6: Data Validation ✅ / ❌
- [ ] Try submitting form with invalid phone format
- [ ] Try extremely long address text
- [ ] Leave contact fields empty (should work)
- [ ] Verify all validation behaves correctly

## 🔍 VERIFICATION COMMANDS

After testing, run these commands to verify:

```bash
# Check total records with contact info
sqlite3 database.sqlite "SELECT COUNT(*) FROM convicts WHERE phone_number IS NOT NULL OR relative_phone_number IS NOT NULL OR address IS NOT NULL;"

# View recent test data
sqlite3 database.sqlite "SELECT tc_no, first_name, last_name, phone_number FROM convicts ORDER BY rowid DESC LIMIT 5;"
```

## ✅ SUCCESS CRITERIA

All tests should:
- ✅ Execute without errors
- ✅ Display contact fields properly  
- ✅ Save data correctly
- ✅ Show data in exports
- ✅ Allow editing of contact info
- ✅ Display contact info on signature page

## 📝 NOTES

- Contact fields are optional (can be empty)
- Phone inputs should have tel type
- Address should be textarea for multi-line
- Turkish labels should display correctly
- Excel import/export should preserve data

**Testing Complete:** ___/___/2024  
**Tested By:** ________________  
**Status:** PASS / FAIL  
**Notes:** ________________________
