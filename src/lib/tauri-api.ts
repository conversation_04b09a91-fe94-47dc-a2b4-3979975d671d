import { invoke } from '@tauri-apps/api/core';
import type {
  User,
  Convict,
  Signature,
  SignaturePeriod,
  InsertUser,
  InsertConvict,
  InsertSignature,
  InsertSignaturePeriod,
  DashboardStats,
  CompletedSignature,
  ExpectedSignature,
  ViolationRecord,
  ViolationReportItem,
  Exemption,
  InsertExemption,
} from '../shared/schema';

// User operations
export const getUsers = async (): Promise<User[]> => {
  return await invoke('get_users');
};

export const createUser = async (user: InsertUser): Promise<User> => {
  return await invoke('create_new_user', { user });
};

export const updateUser = async (id: number, user_data: Partial<InsertUser>): Promise<User> => {
  return await invoke('update_user_cmd', { id, userData: user_data });
};

export const deleteUser = async (id: number): Promise<boolean> => {
  return await invoke('delete_user_cmd', { id });
};

export const authenticateUser = async (
  username: string,
  password: string
): Promise<User | null> => {
  return await invoke('authenticate_user', { username, password });
};

// Convict operations
export const getConvicts = async (): Promise<Convict[]> => {
  return await invoke('get_convicts');
};

export const getConvictById = async (id: number): Promise<Convict | null> => {
  return await invoke('get_convict_by_id_cmd', { id });
};

// Get convict with signature periods for detailed view
export const getConvictWithDetails = async (id: number): Promise<Convict & { activePeriods: SignaturePeriod[] } | null> => {
  const convict = await getConvictById(id);
  if (!convict) return null;
  
  const activePeriods = await getConvictSignaturePeriods(id);
  return {
    ...convict,
    activePeriods
  };
};

export const searchConvictByTcNo = async (tcNo: string): Promise<Convict | null> => {
  return await invoke('search_convict_by_tc', { tcNo });
};

export const searchConvictsByName = async (firstName: string, lastName?: string): Promise<Convict[]> => {
  return await invoke('search_convicts_by_name', { firstName, lastName: lastName || '' });
};

export const searchConvictsByFileNumber = async (fileNumber: string): Promise<Convict[]> => {
  return await invoke('search_convicts_by_file_number', { fileNumber });
};

export const createConvict = async (convict: InsertConvict): Promise<Convict> => {
  return await invoke('create_new_convict', { convict });
};

export const bulkCreateConvicts = async (convicts: InsertConvict[]): Promise<Convict[]> => {
  return await invoke('bulk_create_convicts', { convicts });
};

export const bulkUpsertConvicts = async (convicts: InsertConvict[]): Promise<Convict[]> => {
  return await invoke('bulk_upsert_convicts', { convicts });
};

export const updateConvict = async (id: number, convict: InsertConvict): Promise<Convict> => {
  return await invoke('update_convict_data', { id, convict });
};

export const deleteConvict = async (id: number): Promise<boolean> => {
  return await invoke('delete_convict_data', { id });
};

export const bulkDeleteConvicts = async (ids: number[]): Promise<number> => {
  return await invoke('bulk_delete_convicts_data', { ids });
};

// Signature operations
export const createSignature = async (signature: InsertSignature): Promise<Signature> => {
  return await invoke('create_new_signature', { signature });
};

export const bulkCreateSignatures = async (signatures: InsertSignature[]): Promise<Signature[]> => {
  return await invoke('bulk_create_signatures_with_transaction', { signatures });
};

export const getConvictSignatures = async (convictId: number): Promise<Signature[]> => {
  return await invoke('get_convict_signatures', { convictId });
};

export const deleteSignature = async (id: number): Promise<boolean> => {
  return await invoke('delete_signature_data', { id });
};

// Signature Period operations
export const createSignaturePeriod = async (period: InsertSignaturePeriod): Promise<SignaturePeriod> => {
  return await invoke('create_new_signature_period', { period });
};

export const updateSignaturePeriod = async (id: number, period: InsertSignaturePeriod): Promise<SignaturePeriod> => {
  return await invoke('update_signature_period_data', { id, period });
};

export const deleteSignaturePeriod = async (id: number): Promise<boolean> => {
  return await invoke('delete_signature_period_data', { id });
};

export const getConvictSignaturePeriods = async (convictId: number): Promise<SignaturePeriod[]> => {
  return await invoke('get_signature_periods', { convictId });
};

// Dashboard operations
export const getDashboardStats = async (): Promise<DashboardStats> => {
  console.log('🔍 Tauri API: Calling get_dashboard_statistics...');
  try {
    const result = await invoke('get_dashboard_statistics');
    console.log('📊 Tauri API: Result received:', result);
    return result as DashboardStats;
  } catch (error) {
    console.error('❌ Tauri API: Error calling get_dashboard_statistics:', error);
    throw error;
  }
};

export const getExpectedSignaturesForToday = async (): Promise<Convict[]> => {
  return await invoke('get_expected_signatures_for_today');
};

export const getAllExpectedSignaturesForToday = async (): Promise<ExpectedSignature[]> => {
  return await invoke('get_all_expected_signatures_for_today');
};

export const getCompletedSignaturesForToday = async (): Promise<CompletedSignature[]> => {
  return await invoke('get_completed_signatures_for_today');
};

export const getViolationsForLast30Days = async (): Promise<ViolationRecord[]> => {
  return await invoke('get_violations_for_last_30_days');
};

export const getViolationsReport = async (startDate: string, endDate: string): Promise<ViolationReportItem[]> => {
  return await invoke('get_violations_report', { startDateStr: startDate, endDateStr: endDate });
};

export const saveBinaryFile = async (filePath: string, data: Uint8Array): Promise<void> => {
  const dataArray = Array.from(data);
  return await invoke('save_binary_file', { filePath, data: dataArray });
};

// Exemption operations
export const createExemption = async (exemption: InsertExemption): Promise<Exemption> => {
  return await invoke('create_new_exemption', { exemption });
};

export const getConvictExemptions = async (convictId: number): Promise<Exemption[]> => {
  return await invoke('get_convict_exemptions', { convictId });
};

export const getActiveExemptionsForDate = async (convictId: number, date: string): Promise<Exemption[]> => {
  return await invoke('get_active_exemptions_for_date', { convictId, date });
};

export const updateExemption = async (id: number, exemption: InsertExemption): Promise<Exemption> => {
  return await invoke('update_exemption_data', { id, exemption });
};

export const deleteExemption = async (id: number): Promise<boolean> => {
  return await invoke('delete_exemption_data', { id });
};

// Bulk import operations
export const bulkCreateSignaturePeriods = async (periods: InsertSignaturePeriod[]): Promise<SignaturePeriod[]> => {
  return await invoke('bulk_create_signature_periods_cmd', { periods });
};

export const bulkCreateExemptions = async (exemptions: InsertExemption[]): Promise<Exemption[]> => {
  return await invoke('bulk_create_exemptions_cmd', { exemptions });
};

// Get convict full details (for bulk export)
export const getConvictFullDetails = async (): Promise<any[]> => {
  return await invoke('get_convict_full_details_cmd');
};

export default {
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  authenticateUser,
  getConvicts,
  getConvictById,
  getConvictWithDetails,
  searchConvictByTcNo,
  searchConvictsByName,
  searchConvictsByFileNumber,
  createConvict,
  bulkCreateConvicts,
  bulkUpsertConvicts,
  updateConvict,
  deleteConvict,
  bulkDeleteConvicts,
  createSignature,
  bulkCreateSignatures,
  deleteSignature,
  getConvictSignatures,
  createSignaturePeriod,
  updateSignaturePeriod,
  deleteSignaturePeriod,
  getConvictSignaturePeriods,
  getDashboardStats,
  getExpectedSignaturesForToday,
  getAllExpectedSignaturesForToday,
  getCompletedSignaturesForToday,
  getViolationsForLast30Days,
  getViolationsReport,
  saveBinaryFile,
  createExemption,
  getConvictExemptions,
  getActiveExemptionsForDate,
  updateExemption,
  deleteExemption,
  bulkCreateSignaturePeriods,
  bulkCreateExemptions,
  getConvictFullDetails,
};
