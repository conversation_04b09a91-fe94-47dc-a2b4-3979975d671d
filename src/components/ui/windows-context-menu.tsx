import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '@/lib/utils';

interface ContextMenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  separator?: boolean;
  shortcut?: string;
}

interface WindowsContextMenuProps {
  items: ContextMenuItem[];
  onClose: () => void;
  x: number;
  y: number;
}

export function useWindowsContextMenu(items: ContextMenuItem[]) {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    setPosition({ x: e.clientX, y: e.clientY });
    setIsOpen(true);
  };
  
  const handleClose = () => {
    setIsOpen(false);
  };
  
  return {
    isOpen,
    position,
    handleContextMenu,
    handleClose,
    contextMenuProps: {
      items,
      onClose: handleClose,
      x: position.x,
      y: position.y,
    },
  };
}

export function WindowsContextMenu({ items, onClose, x, y }: WindowsContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  
  // Close menu when clicking outside
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        onClose();
      }
    };
    
    document.addEventListener('mousedown', handleOutsideClick);
    return () => document.removeEventListener('mousedown', handleOutsideClick);
  }, [onClose]);
  
  // Close menu when pressing Escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);
  
  // Adjust position if menu would go off screen
  const adjustedPosition = useRef({ x, y });
  
  useEffect(() => {
    if (menuRef.current) {
      const menuRect = menuRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      if (x + menuRect.width > viewportWidth) {
        adjustedPosition.current.x = viewportWidth - menuRect.width - 5;
      }
      
      if (y + menuRect.height > viewportHeight) {
        adjustedPosition.current.y = viewportHeight - menuRect.height - 5;
      }
    }
  }, [x, y]);
  
  return createPortal(
    <div 
      ref={menuRef}
      className="windows-context-menu absolute z-50"
      style={{ 
        left: `${adjustedPosition.current.x}px`, 
        top: `${adjustedPosition.current.y}px` 
      }}
    >
      {items.map((item, index) => {
        if (item.separator) {
          return <div key={`sep-${index}`} className="windows-context-menu-separator" />;
        }
        
        return (
          <div
            key={item.id}
            className={cn(
              "windows-context-menu-item",
              item.disabled && "opacity-50 pointer-events-none"
            )}
            onClick={() => {
              if (!item.disabled && item.onClick) {
                item.onClick();
                onClose();
              }
            }}
          >
            {item.icon && (
              <span className="w-4 h-4">{item.icon}</span>
            )}
            <span className="flex-1">{item.label}</span>
            {item.shortcut && (
              <span className="text-gray-500 text-xs">{item.shortcut}</span>
            )}
          </div>
        );
      })}
    </div>,
    document.body
  );
}
