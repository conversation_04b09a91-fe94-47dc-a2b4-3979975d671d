#!/bin/bash

# Contact Fields Integration Test Script
# This script tests all aspects of the new contact fields implementation

echo "🧪 Starting Contact Fields Integration Tests"
echo "============================================="

# Database connectivity test
echo "1. Testing database schema..."
cd /Users/<USER>/eits
DB_PATH="database.sqlite"

# Check if contact fields exist in database
echo "   Checking contact fields in database schema..."
sqlite3 $DB_PATH << 'EOF'
.headers on
.mode column
PRAGMA table_info(convicts);
EOF

echo -e "\n2. Testing database content..."
# Check if there are existing convicts with contact info
sqlite3 $DB_PATH << 'EOF'
.headers on
.mode column
SELECT COUNT(*) as total_convicts FROM convicts;
SELECT COUNT(*) as convicts_with_phone FROM convicts WHERE phone_number IS NOT NULL;
SELECT COUNT(*) as convicts_with_relative_phone FROM convicts WHERE relative_phone_number IS NOT NULL;
SELECT COUNT(*) as convicts_with_address FROM convicts WHERE address IS NOT NULL;
EOF

echo -e "\n3. Testing application status..."
# Check if application is running
TAURI_PID=$(ps aux | grep "tauri dev" | grep -v grep | awk '{print $2}')
if [ -n "$TAURI_PID" ]; then
    echo "   ✅ Application is running (PID: $TAURI_PID)"
else
    echo "   ❌ Application is not running"
fi

# Check application accessibility
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:1420 || echo "000")
if [ "$HTTP_STATUS" = "200" ]; then
    echo "   ✅ Application is accessible at http://localhost:1420"
else
    echo "   ❌ Application is not accessible (HTTP Status: $HTTP_STATUS)"
fi

echo -e "\n4. Testing Excel test file..."
# Check if test Excel file exists and is valid
if [ -f "test_contact_fields.xlsx" ]; then
    echo "   ✅ Test Excel file exists"
    echo "   File size: $(ls -lh test_contact_fields.xlsx | awk '{print $5}')"
else
    echo "   ❌ Test Excel file not found"
fi

echo -e "\n5. Testing TypeScript compilation..."
# Check for TypeScript errors
npm run type-check 2>&1 | head -10

echo -e "\n6. Summary of contact fields implementation:"
echo "   • Database schema: 3 new contact fields added"
echo "   • Rust backend: All structs and functions updated"
echo "   • Frontend forms: Contact fields added with validation"
echo "   • Excel import/export: Headers and parsing updated"
echo "   • Display components: Contact info display added"

echo -e "\n🎯 Ready for manual testing:"
echo "   1. Open http://localhost:1420 in browser"
echo "   2. Test Excel import with test_contact_fields.xlsx"
echo "   3. Test form input with contact information"
echo "   4. Test Excel export functionality"
echo "   5. Test signature recording page contact display"

echo -e "\n📁 Test files available:"
echo "   • test_contact_fields.csv (CSV format)"
echo "   • test_contact_fields.xlsx (Excel format)"
echo "   • CONTACT_FIELDS_TEST_PLAN.md (Test plan)"

echo -e "\n✅ Integration test complete!"
