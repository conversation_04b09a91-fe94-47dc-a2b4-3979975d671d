import { useQuery } from '@tanstack/react-query';
import { getDashboardStats } from '@/lib/tauri-api';

export default function WindowsStatusBar() {
  const { data: stats } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: getDashboardStats,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  return (
    <div className="bg-white/15 backdrop-blur-lg border-t border-white/10 px-4 py-2 flex items-center justify-between text-sm text-gray-700 shadow-sm">
      {/* Left side - Status messages */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="font-medium">Hazır</span>
        </div>
        {stats && (
          <>
            <div className="w-px h-4 bg-white/30"></div>
            <div className="flex items-center space-x-4">
              <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md">
                Toplam Hükümlü: <span className="font-semibold text-blue-600">{stats.total_convicts || 0}</span>
              </span>
              <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md">
                Aktif Takip: <span className="font-semibold text-green-600">{stats.active_convicts || 0}</span>
              </span>
              <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md">
                Bugün Beklenen: <span className="font-semibold text-orange-600">{stats.expected_today || 0}</span>
              </span>
              <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md">
                Bugün Tamamlanan: <span className="font-semibold text-purple-600">{stats.completed_today || 0}</span>
              </span>
              <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md">
                İhlaller: <span className="font-semibold text-red-600">{stats.violations || 0}</span>
              </span>
            </div>
          </>
        )}
      </div>
      
      {/* Right side - System info */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-3 px-3 py-1 bg-white/20 backdrop-blur-sm rounded-md">
          <span className="font-medium">
            {new Date().toLocaleDateString('tr-TR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            })}
          </span>
          <div className="w-px h-4 bg-white/30"></div>
          <span className="font-medium">
            {new Date().toLocaleTimeString('tr-TR', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </span>
        </div>
        <div className="px-3 py-1 bg-blue-100/80 backdrop-blur-sm rounded-md">
          <span className="text-blue-700 font-semibold">EİTS v1.0</span>
        </div>
      </div>
    </div>
  );
}
