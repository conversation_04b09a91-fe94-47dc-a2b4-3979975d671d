import { ReactNode } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import WindowsMenuBar from './WindowsMenuBar';
import WindowsToolbar from './WindowsToolbar';
import WindowsStatusBar from './WindowsStatusBar';
import WindowsNavigationPanel from './WindowsNavigationPanel';
import WindowControls from './WindowControls';
import useKeyboardShortcuts from '@/hooks/useKeyboardShortcuts';
import { KeyboardShortcutsDialog } from '@/components/common/KeyboardShortcutsDialog';
import { useKeyboardShortcutsDialog } from '@/hooks/useKeyboardShortcutsDialog';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { useUIStore } from '@/store/uiStore';

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  // Initialize keyboard shortcuts
  useKeyboardShortcuts();
  
  // Initialize keyboard shortcuts dialog
  const { isOpen, closeDialog } = useKeyboardShortcutsDialog();

  // Get UI visibility states
  const isToolbarVisible = useUIStore((state) => state.isToolbarVisible);
  const isStatusBarVisible = useUIStore((state) => state.isStatusBarVisible);

  // Handle title bar double click for maximize/restore
  const handleTitleBarDoubleClick = async () => {
    const appWindow = getCurrentWindow();
    await appWindow.toggleMaximize();
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 font-segoe">
      {/* Windows 11 Title Bar */}
      <div 
        className="bg-white/20 backdrop-blur-xl border-b border-white/10 px-4 py-2 flex items-center justify-between h-12 shadow-sm cursor-default select-none"
        onDoubleClick={handleTitleBarDoubleClick}
        data-tauri-drag-region
      >
        <div className="flex items-center space-x-3">
          <CheckCircleIcon className="h-5 w-5 text-blue-600" />
          <span className="text-sm font-medium text-gray-800">EİTS - Elektronik İmza Takip Sistemi</span>
        </div>
        <div className="flex items-center">
          <WindowControls />
        </div>
      </div>
      
      {/* Windows Menu Bar */}
      <WindowsMenuBar />
      
      {/* Windows Toolbar */}
      {isToolbarVisible && <WindowsToolbar />}
      
      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Navigation Panel */}
        <WindowsNavigationPanel />
        
        {/* Content Area */}
        <main className="flex-1 overflow-auto bg-white/80 backdrop-blur-sm border-l border-white/20 shadow-lg">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
      
      {/* Windows Status Bar */}
      {isStatusBarVisible && <WindowsStatusBar />}
      
      {/* Keyboard Shortcuts Dialog */}
      <KeyboardShortcutsDialog isOpen={isOpen} onClose={closeDialog} />
    </div>
  );
}
