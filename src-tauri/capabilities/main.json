{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "main-capability", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "core:window:default", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-focus", "core:window:allow-unminimize", "core:window:allow-set-title", "core:window:allow-set-size", "core:window:allow-set-position", "core:window:allow-set-decorations", "core:window:allow-set-fullscreen", "core:window:allow-set-min-size", "core:window:allow-set-max-size", "core:window:allow-set-always-on-top", "core:window:allow-set-theme", "core:window:allow-start-dragging", "core:window:allow-toggle-maximize", "dialog:allow-open", "dialog:allow-save", "dialog:allow-message", "dialog:allow-ask", "dialog:allow-confirm", "fs:allow-read-text-file", "fs:allow-write-text-file", "fs:default"]}