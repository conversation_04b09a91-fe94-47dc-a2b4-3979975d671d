import { useEffect, useCallback } from 'react';
import { listen } from '@tauri-apps/api/event';
import { useQueryClient } from '@tanstack/react-query';

interface DataChangeEvent {
  event_type: string;
  table: string;
  id?: number;
  timestamp: number;
}

export function useRealtime() {
  const queryClient = useQueryClient();

  const handleDataChange = useCallback((event: DataChangeEvent) => {
    console.log('Data change event received:', event);
    
    // Invalidate relevant queries based on table
    switch (event.table) {
      case 'convicts':
        // Invalidate all convict-related queries
        queryClient.invalidateQueries({ queryKey: ['convicts'] });
        queryClient.invalidateQueries({ queryKey: ['convict-search-tc'] });
        queryClient.invalidateQueries({ queryKey: ['convict-search-name'] });
        // Also invalidate dashboard stats as they include convict counts
        queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
        break;
      
      case 'signatures':
        // Invalidate signature-related queries
        queryClient.invalidateQueries({ queryKey: ['signatures'] });
        queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
        queryClient.invalidateQueries({ queryKey: ['expected-signatures-today'] });
        queryClient.invalidateQueries({ queryKey: ['expected-signatures-today-page'] });
        queryClient.invalidateQueries({ queryKey: ['completed-signatures-today'] });
        break;
      
      case 'signature_periods':
        // Invalidate signature period queries
        queryClient.invalidateQueries({ queryKey: ['signature-periods'] });
        break;
      
      case 'users':
        // Invalidate user queries
        queryClient.invalidateQueries({ queryKey: ['users'] });
        break;
    }
  }, [queryClient]);

  useEffect(() => {
    let unlisten: (() => void) | undefined;

    const setupListener = async () => {
      try {
        unlisten = await listen<DataChangeEvent>('data-change', (event) => {
          handleDataChange(event.payload);
        });
        console.log('Real-time event listener setup successfully');
      } catch (error) {
        console.error('Failed to setup real-time event listener:', error);
      }
    };

    setupListener();

    return () => {
      if (unlisten) {
        unlisten();
        console.log('Real-time event listener cleaned up');
      }
    };
  }, [handleDataChange]);

  return {
    // Could expose methods for manual refresh if needed
    refreshAll: () => {
      queryClient.invalidateQueries();
    }
  };
}
