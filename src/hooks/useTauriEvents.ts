import { useEffect } from 'react';
import { useLocation } from 'wouter';

declare global {
  interface Window {
    __TAURI__?: unknown;
  }
}

/**
 * Hook to listen for events from the Tauri backend
 */
export function useTauriEvents() {
  const [, setLocation] = useLocation();

  useEffect(() => {
    // Check if we're running in a Tauri environment
    const isTauri = window['__TAURI__'] !== undefined;
    
    if (!isTauri) {
      return; // Not running in Tauri
    }
    
    const initTauriEvents = async () => {
      try {
        // Import Tauri API
        const { listen } = await import('@tauri-apps/api/event');
        
        // Listen for navigation events from the system tray
        const unlisten = await listen('navigate', (event) => {
          const path = event.payload as string;
          if (path) {
            setLocation(path);
          }
        });
        
        // Clean up listener when component unmounts
        return () => {
          unlisten();
        };
      } catch (error) {
        console.error('Failed to initialize Tauri events:', error);
      }
    };
    
    initTauriEvents();
  }, [setLocation]);
}
