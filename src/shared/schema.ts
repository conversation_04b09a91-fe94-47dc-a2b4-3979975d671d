import { z } from "zod";
import { sqliteTable, integer, text } from "drizzle-orm/sqlite-core";
import { sql } from "drizzle-orm";

// Drizzle SQLite Tables
export const users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role", { enum: ["USER", "ADMIN"] }).notNull(),
  is_active: integer("is_active", { mode: "boolean" }).notNull().default(true),
  created_at: text("created_at").default(sql`CURRENT_TIMESTAMP`),
});

export const convicts = sqliteTable("convicts", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  tc_no: text("tc_no").notNull().unique(),
  first_name: text("first_name").notNull(),
  last_name: text("last_name").notNull(),
  supervision_start_date: text("supervision_start_date").notNull(),
  supervision_end_date: text("supervision_end_date").notNull(),
  phone_number: text("phone_number"),
  relative_phone_number: text("relative_phone_number"),
  address: text("address"),
  file_number: text("file_number"), // Case file number (e.g., "2025/34 NKL")
  is_active: integer("is_active", { mode: "boolean" }).notNull().default(true),
  notes: text("notes"),
  created_at: text("created_at").default(sql`CURRENT_TIMESTAMP`),
  updated_at: text("updated_at").default(sql`CURRENT_TIMESTAMP`),
});

export const signaturePeriods = sqliteTable("signature_periods", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  convict_id: integer("convict_id").notNull().references(() => convicts.id),
  start_date: text("start_date").notNull(),
  end_date: text("end_date").notNull(),
  frequency_type: text("frequency_type", { enum: ["WEEKLY", "X_DAYS", "MONTHLY_SPECIFIC"] }).notNull(),
  frequency_value: text("frequency_value").notNull(),
  reference_date: text("reference_date"),
  time_start: text("time_start"), // Start time like "10:00"
  time_end: text("time_end"), // End time like "22:00"
  allowed_days: text("allowed_days"), // JSON array of allowed weekdays [0,2,4] for Mon,Wed,Fri
  is_active: integer("is_active", { mode: "boolean" }).notNull().default(true),
  created_at: text("created_at").default(sql`CURRENT_TIMESTAMP`),
});

export const signatures = sqliteTable("signatures", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  convict_id: integer("convict_id").notNull().references(() => convicts.id),
  signature_date: text("signature_date").notNull(),
  signature_time: text("signature_time").notNull(),
  recorded_by: integer("recorded_by").references(() => users.id),
  created_at: text("created_at").default(sql`CURRENT_TIMESTAMP`),
});

export const exemptions = sqliteTable("exemptions", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  convict_id: integer("convict_id").notNull().references(() => convicts.id),
  exemption_type: text("exemption_type", { enum: ["LEAVE", "MEDICAL_REPORT"] }).notNull(),
  start_date: text("start_date").notNull(),
  end_date: text("end_date").notNull(),
  description: text("description"),
  document_path: text("document_path"),
  created_by: integer("created_by").references(() => users.id),
  is_active: integer("is_active", { mode: "boolean" }).notNull().default(true),
  created_at: text("created_at").default(sql`CURRENT_TIMESTAMP`),
  updated_at: text("updated_at").default(sql`CURRENT_TIMESTAMP`),
});

// Types inferred from the schema
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Convict = typeof convicts.$inferSelect;
export type NewConvict = typeof convicts.$inferInsert;
export type SignaturePeriod = typeof signaturePeriods.$inferSelect;
export type NewSignaturePeriod = typeof signaturePeriods.$inferInsert;
export type Signature = typeof signatures.$inferSelect;
export type NewSignature = typeof signatures.$inferInsert;
export type Exemption = typeof exemptions.$inferSelect;
export type NewExemption = typeof exemptions.$inferInsert;

// Zod validation schemas
export const insertUserSchema = z.object({
  username: z.string().min(1),
  password: z.string().min(1),
  role: z.enum(["USER", "ADMIN"]),
  is_active: z.boolean().optional(),
});

export const insertConvictSchema = z.object({
  tc_no: z.string().min(11).max(11),
  first_name: z.string().min(1),
  last_name: z.string().min(1),
  supervision_start_date: z.string(),
  supervision_end_date: z.string(),
  phone_number: z.string().optional(),
  relative_phone_number: z.string().optional(),
  address: z.string().optional(),
  file_number: z.string().optional(),
  is_active: z.boolean().optional(),
  notes: z.string().optional(),
});

export const insertSignaturePeriodSchema = z.object({
  convict_id: z.number(),
  start_date: z.string(),
  end_date: z.string(),
  frequency_type: z.enum(["WEEKLY", "X_DAYS", "MONTHLY_SPECIFIC"]),
  frequency_value: z.string(),
  reference_date: z.string().optional(),
  time_start: z.string().optional(), // Format: "HH:MM"
  time_end: z.string().optional(), // Format: "HH:MM"
  allowed_days: z.string().optional(), // JSON array string
  is_active: z.boolean().optional(),
});

export const insertSignatureSchema = z.object({
  convict_id: z.number(),
  signature_date: z.string(),
  signature_time: z.string(),
  recorded_by: z.number().optional(),
});

export const insertExemptionSchema = z.object({
  convict_id: z.number(),
  exemption_type: z.enum(["LEAVE", "MEDICAL_REPORT"]),
  start_date: z.string(),
  end_date: z.string(),
  description: z.string().optional(),
  document_path: z.string().optional(),
  created_by: z.number().optional(),
  is_active: z.boolean().optional(),
});

// Insert types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertConvict = z.infer<typeof insertConvictSchema>;
export type InsertSignaturePeriod = z.infer<typeof insertSignaturePeriodSchema>;
export type InsertSignature = z.infer<typeof insertSignatureSchema>;
export type InsertExemption = z.infer<typeof insertExemptionSchema>;

// Extended types for API responses
export type ConvictWithDetails = Convict & {
  fullName: string;
  activePeriods: SignaturePeriod[];
  lastSignature?: Signature;
};

export type DashboardStats = {
  total_convicts: number;
  active_convicts: number;
  expected_today: number;
  completed_today: number;
  violations: number;
};

export type CompletedSignature = {
  convict_id: number;
  tc_no: string;
  first_name: string;
  last_name: string;
  signature_time: string;
  signature_date: string;
};

export type ExpectedSignature = {
  convict_id: number;
  tc_no: string;
  first_name: string;
  last_name: string;
  has_signed: boolean;
  signature_time: string | null;
};

export type ViolationRecord = {
  convict_id: number;
  tc_no: string;
  first_name: string;
  last_name: string;
  violation_date: string;
  expected_frequency: string;
};

export type ViolationReportItem = {
  convict: Convict;
  violationDate: string;
  expectedFrequency: string;
};

export type DailyReport = {
  date: string;
  expected: Array<{
    convict: Convict;
    frequency: string;
  }>;
  completed: Array<{
    convict: Convict;
    signature: Signature;
  }>;
  violations: Array<{
    convict: Convict;
    frequency: string;
  }>;
};

// Full convict details for bulk export
export type ConvictFullDetails = {
  id: number;
  tc_no: string;
  first_name: string;
  last_name: string;
  phone_number?: string;
  relative_phone_number?: string;
  address?: string;
  file_number?: string;
  supervision_start_date: string;
  supervision_end_date: string;
  is_active: boolean;
  notes?: string;
  signature_periods?: Array<{
    id: number;
    start_date: string;
    end_date: string;
    frequency_type: string;
    frequency_value: string;
    reference_date?: string;
    is_active: boolean;
  }>;
  exemptions?: Array<{
    id: number;
    exemption_type: string;
    start_date: string;
    end_date: string;
    description?: string;
    document_path?: string;
    is_active: boolean;
  }>;
};
