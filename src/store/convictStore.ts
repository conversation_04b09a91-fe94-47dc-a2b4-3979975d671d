import { create } from 'zustand';
import type { ConvictWithDetails } from '@shared/schema';

interface ConvictState {
  selectedConvict: ConvictWithDetails | null;
  searchResults: ConvictWithDetails[];
  totalCount: number;
  currentPage: number;
  searchFilters: {
    tcNo: string;
    firstName: string;
    lastName: string;
    isActive?: boolean;
  };
  setSelectedConvict: (convict: ConvictWithDetails | null) => void;
  setSearchResults: (results: ConvictWithDetails[], total: number) => void;
  setCurrentPage: (page: number) => void;
  setSearchFilters: (filters: Partial<ConvictState['searchFilters']>) => void;
  clearSearchFilters: () => void;
}

export const useConvictStore = create<ConvictState>((set) => ({
  selectedConvict: null,
  searchResults: [],
  totalCount: 0,
  currentPage: 1,
  searchFilters: {
    tcNo: '',
    firstName: '',
    lastName: '',
    isActive: undefined,
  },
  setSelectedConvict: (convict) => set({ selectedConvict: convict }),
  setSearchResults: (results, total) => set({ searchResults: results, totalCount: total }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setSearchFilters: (filters) => set((state) => ({
    searchFilters: { ...state.searchFilters, ...filters }
  })),
  clearSearchFilters: () => set({
    searchFilters: {
      tcNo: '',
      firstName: '',
      lastName: '',
      isActive: undefined,
    }
  }),
}));
