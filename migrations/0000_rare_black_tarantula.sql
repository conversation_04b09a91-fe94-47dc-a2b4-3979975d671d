CREATE TABLE `convicts` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`tc_no` text NOT NULL,
	`first_name` text NOT NULL,
	`last_name` text NOT NULL,
	`supervision_start_date` text NOT NULL,
	`supervision_end_date` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL,
	`notes` text,
	`created_at` text DEFAULT CURRENT_TIMESTAMP,
	`updated_at` text DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
CREATE UNIQUE INDEX `convicts_tc_no_unique` ON `convicts` (`tc_no`);--> statement-breakpoint
CREATE TABLE `signature_periods` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`convict_id` integer NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text NOT NULL,
	`frequency_type` text NOT NULL,
	`frequency_value` text NOT NULL,
	`reference_date` text,
	`is_active` integer DEFAULT true NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP,
	FOREI<PERSON><PERSON> KEY (`convict_id`) REFERENCES `convicts`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `signatures` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`convict_id` integer NOT NULL,
	`signature_date` text NOT NULL,
	`signature_time` text NOT NULL,
	`recorded_by` integer,
	`created_at` text DEFAULT CURRENT_TIMESTAMP,
	FOREIGN KEY (`convict_id`) REFERENCES `convicts`(`id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`recorded_by`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`username` text NOT NULL,
	`password` text NOT NULL,
	`role` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL,
	`created_at` text DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_unique` ON `users` (`username`);