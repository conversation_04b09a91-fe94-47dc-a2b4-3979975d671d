// Test ManageSignaturePeriodsPage grouping functionality
const Database = require('better-sqlite3');

// <PERSON> grouping fonksiyonunu kopyalayalım
function groupSignaturePeriods(periods) {
  const groups = new Map();
  
  // Group periods by date range, type, and time
  periods.forEach(period => {
    const key = `${period.start_date}_${period.end_date}_${period.frequency_type}_${period.time_start || ''}_${period.time_end || ''}`;
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key).push(period);
  });
  
  // Convert groups to consolidated periods
  const groupedPeriods = Array.from(groups.values()).map(groupPeriods => {
    // Use the first period as base
    const basePeriod = groupPeriods[0];
    
    // Merge frequency values based on type
    let mergedFrequencyValue = '';
    if (basePeriod.frequency_type === 'WEEKLY') {
      // Collect all weekly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      mergedFrequencyValue = Array.from(allDays).join(',');
    } else if (basePeriod.frequency_type === 'MONTHLY_SPECIFIC') {
      // Collect all monthly days and deduplicate
      const allDays = new Set();
      groupPeriods.forEach(period => {
        if (period.frequency_value) {
          period.frequency_value.split(',').forEach(day => {
            allDays.add(day.trim());
          });
        }
      });
      // Sort numerically for monthly days
      const sortedDays = Array.from(allDays).sort((a, b) => parseInt(a) - parseInt(b));
      mergedFrequencyValue = sortedDays.join(',');
    } else {
      // For X_DAYS type, use the first period's value
      mergedFrequencyValue = basePeriod.frequency_value;
    }
    
    // Return a merged period
    return {
      ...basePeriod,
      frequency_value: mergedFrequencyValue
    };
  });
  
  return groupedPeriods;
}

console.log('🧪 ManageSignaturePeriodsPage Gruplama Testi');
console.log('==============================================');

// Database connection
const db = new Database('./database.sqlite', { readonly: true });

// Test convict (108 or 109 with test data)
const testConvictId = 108;

console.log(`\n🔍 Hükümlü ${testConvictId} için periyotları yükleniyor...`);

// Get signature periods for test convict
const periods = db.prepare(`
  SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, 
         reference_date, is_active, time_start, time_end, allowed_days
  FROM signature_periods 
  WHERE convict_id = ? AND is_active = 1
  ORDER BY start_date, frequency_type
`).all(testConvictId);

console.log(`\n📋 Veritabanından yüklenen periyotlar (${periods.length} adet):`);
periods.forEach((period, index) => {
  console.log(`${index + 1}. ID: ${period.id}`);
  console.log(`   Tarih: ${period.start_date} - ${period.end_date}`);
  console.log(`   Tip: ${period.frequency_type}`);
  console.log(`   Değer: ${period.frequency_value}`);
  console.log(`   Saat: ${period.time_start || 'Yok'} - ${period.time_end || 'Yok'}`);
  console.log('');
});

if (periods.length === 0) {
  console.log('❌ Test verisi bulunamadı! Önce test verisi ekleyin.');
  db.close();
  process.exit(1);
}

// Convert to expected format for grouping function
const formattedPeriods = periods.map(p => ({
  ...p,
  is_active: Boolean(p.is_active)
}));

console.log('\n🔄 Gruplama işlemi uygulanıyor...');

// Apply grouping
try {
  const groupedPeriods = groupSignaturePeriods(formattedPeriods);
  
  console.log(`\n✅ Gruplama tamamlandı:`);
  console.log(`   Orijinal periyot sayısı: ${periods.length}`);
  console.log(`   Gruplandırılmış periyot sayısı: ${groupedPeriods.length}`);
  
  console.log(`\n📊 Gruplandırılmış periyotlar:`);
  groupedPeriods.forEach((period, index) => {
    console.log(`${index + 1}. Grup:`);
    console.log(`   Tarih: ${period.start_date} - ${period.end_date}`);
    console.log(`   Tip: ${period.frequency_type}`);
    console.log(`   Değer: ${period.frequency_value}`);
    console.log(`   Saat: ${period.time_start || 'Yok'} - ${period.time_end || 'Yok'}`);
    
    // Find how many original periods this group represents
    const matchingOriginals = periods.filter(p => 
      p.start_date === period.start_date &&
      p.end_date === period.end_date &&
      p.frequency_type === period.frequency_type &&
      p.time_start === period.time_start &&
      p.time_end === period.time_end
    );
    
    console.log(`   Grupladığı periyot sayısı: ${matchingOriginals.length}`);
    if (matchingOriginals.length > 1) {
      console.log(`   Orijinal değerler: ${matchingOriginals.map(p => p.frequency_value).join(', ')}`);
    }
    console.log('');
  });
  
  // Test cases
  console.log('\n🧪 Test Senaryoları:');
  
  // Test 1: Check if grouping reduces period count
  if (groupedPeriods.length < periods.length) {
    console.log(`✅ Test 1 PASSED: Gruplama başarılı (${periods.length} → ${groupedPeriods.length})`);
  } else if (groupedPeriods.length === periods.length) {
    console.log(`⚠️  Test 1 NEUTRAL: Gruplamaya gerek yok (tüm periyotlar farklı)`);
  } else {
    console.log(`❌ Test 1 FAILED: Gruplama sonucu beklenmedik`);
  }
  
  // Test 2: Check if all original data is preserved
  const totalOriginalFrequencyValues = periods.length;
  const totalGroupedFrequencyValues = groupedPeriods.reduce((sum, period) => {
    return sum + (period.frequency_value.split(',').length || 1);
  }, 0);
  
  if (totalGroupedFrequencyValues >= totalOriginalFrequencyValues) {
    console.log(`✅ Test 2 PASSED: Veri bütünlüğü korundu`);
  } else {
    console.log(`❌ Test 2 FAILED: Veri kaybı oldu`);
  }
  
  // Test 3: Check specific grouping for WEEKLY periods
  const weeklyGroups = groupedPeriods.filter(p => p.frequency_type === 'WEEKLY');
  const originalWeeklyPeriods = periods.filter(p => p.frequency_type === 'WEEKLY');
  
  if (weeklyGroups.length > 0 && weeklyGroups.length <= originalWeeklyPeriods.length) {
    console.log(`✅ Test 3 PASSED: WEEKLY periyotlar doğru gruplandı`);
    weeklyGroups.forEach(group => {
      const days = group.frequency_value.split(',');
      console.log(`   Grup: ${days.join(', ')} (${days.length} gün)`);
    });
  } else {
    console.log(`⚠️  Test 3 SKIPPED: WEEKLY periyot bulunamadı`);
  }
  
  console.log('\n🎯 Sonuç:');
  console.log('ManageSignaturePeriodsPage için gruplama fonksiyonu hazır!');
  console.log('Artık DataTable\'da gruplandırılmış veriler görüntülenecek.');
  
} catch (error) {
  console.error('❌ Gruplama hatası:', error.message);
  console.error(error.stack);
}

db.close();
