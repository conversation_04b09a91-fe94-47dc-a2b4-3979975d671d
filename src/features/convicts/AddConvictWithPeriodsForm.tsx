import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { validateTcNo } from '@/lib/utils';
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import type { InsertConvict, InsertSignaturePeriod } from '@shared/schema';

const weekDays = [
  { value: 0, label: 'Pazartesi', backendValue: 'MONDAY' },
  { value: 1, label: 'Salı', backendValue: 'TUESDAY' },
  { value: 2, label: 'Çarşamba', backendValue: 'WEDNESDAY' },
  { value: 3, label: 'Perşembe', backendValue: 'THURSDAY' },
  { value: 4, label: 'Cuma', backendValue: 'FRIDAY' },
  { value: 5, label: 'Cumartesi', backendValue: 'SATURDAY' },
  { value: 6, label: 'Pazar', backendValue: 'SUNDAY' },
];

const signaturePeriodSchema = z.object({
  startDate: z.string().min(1, 'Başlangıç tarihi gereklidir'),
  endDate: z.string().min(1, 'Bitiş tarihi gereklidir'),
  frequencyType: z.enum(['WEEKLY', 'X_DAYS', 'MONTHLY_SPECIFIC'], {
    required_error: 'Sıklık türü seçiniz',
  }),
  frequencyValue: z.any(), // Keep flexible for now
  referenceDate: z.string().optional(),
  isActive: z.boolean().default(true),
}).refine(
  (data) => new Date(data.endDate) > new Date(data.startDate),
  {
    message: 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır',
    path: ['endDate'],
  }
);

const convictWithPeriodsSchema = z.object({
  // Convict fields
  tcNo: z
    .string()
    .min(11, 'TC Kimlik No 11 haneli olmalıdır')
    .max(11, 'TC Kimlik No 11 haneli olmalıdır')
    .regex(/^\d+$/, 'TC Kimlik No sadece rakam içermelidir')
    .refine(validateTcNo, 'Geçersiz TC Kimlik No'),
  firstName: z.string().min(1, 'Ad gereklidir'),
  lastName: z.string().min(1, 'Soyad gereklidir'),
  phoneNumber: z.string().optional(),
  relativePhoneNumber: z.string().optional(),
  address: z.string().optional(),
  fileNumber: z.string().optional(),
  supervisionStartDate: z.string().min(1, 'Denetim başlangıç tarihi gereklidir'),
  supervisionEndDate: z.string().min(1, 'Denetim bitiş tarihi gereklidir'),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
  // Signature periods
  signaturePeriods: z.array(signaturePeriodSchema).optional().refine(
    (periods) => {
      // If signature periods are provided, they should be valid
      if (!periods || periods.length === 0) return true;
      
      return periods.every(period => {
        // Check frequency value based on type
        if (period.frequencyType === 'WEEKLY') {
          return Array.isArray(period.frequencyValue) && period.frequencyValue.length > 0;
        }
        if (period.frequencyType === 'X_DAYS') {
          const value = typeof period.frequencyValue === 'string' ? parseInt(period.frequencyValue) : period.frequencyValue;
          return !isNaN(value) && value >= 1;
        }
        if (period.frequencyType === 'MONTHLY_SPECIFIC') {
          return typeof period.frequencyValue === 'string' && period.frequencyValue.trim().length > 0;
        }
        return true;
      });
    },
    {
      message: 'İmza periyotlarında geçersiz değerler var',
    }
  ),
}).refine(
  (data) => new Date(data.supervisionEndDate) > new Date(data.supervisionStartDate),
  {
    message: 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır',
    path: ['supervisionEndDate'],
  }
);

type ConvictWithPeriodsFormData = z.infer<typeof convictWithPeriodsSchema>;

interface AddConvictWithPeriodsFormProps {
  onSubmit: (convictData: InsertConvict, periodsData: Omit<InsertSignaturePeriod, 'convict_id'>[]) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export default function AddConvictWithPeriodsForm({
  onSubmit,
  onCancel,
  isLoading = false,
}: AddConvictWithPeriodsFormProps) {
  const [showSignaturePeriods, setShowSignaturePeriods] = useState(false);
  
  const form = useForm<ConvictWithPeriodsFormData>({
    resolver: zodResolver(convictWithPeriodsSchema),
    defaultValues: {
      tcNo: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      relativePhoneNumber: '',
      address: '',
      fileNumber: '',
      supervisionStartDate: '',
      supervisionEndDate: '',
      isActive: true,
      notes: '',
      signaturePeriods: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'signaturePeriods',
  });

  const supervisionStartDate = form.watch('supervisionStartDate');
  const supervisionEndDate = form.watch('supervisionEndDate');

  const addSignaturePeriod = () => {
    append({
      startDate: supervisionStartDate || '',
      endDate: supervisionEndDate || '',
      frequencyType: 'WEEKLY',
      frequencyValue: [1], // Default to Tuesday
      referenceDate: '',
      isActive: true,
    });
  };

  const handleSubmit = (data: ConvictWithPeriodsFormData) => {
    console.log('Form submitted with data:', data);
    console.log('Show signature periods:', showSignaturePeriods);
    console.log('Form validation state:', form.formState);
    console.log('Form errors:', form.formState.errors);
    
    // Map camelCase form data to snake_case API data for convict
    const convictData: InsertConvict = {
      tc_no: data.tcNo,
      first_name: data.firstName,
      last_name: data.lastName,
      phone_number: data.phoneNumber,
      relative_phone_number: data.relativePhoneNumber,
      address: data.address,
      file_number: data.fileNumber,
      supervision_start_date: data.supervisionStartDate,
      supervision_end_date: data.supervisionEndDate,
      is_active: data.isActive,
      notes: data.notes,
    };

    // Process signature periods only if enabled and periods exist
    let periodsData: Omit<InsertSignaturePeriod, 'convict_id'>[] = [];
    
    if (showSignaturePeriods && data.signaturePeriods && data.signaturePeriods.length > 0) {
      console.log('Processing signature periods:', data.signaturePeriods);
      
      periodsData = data.signaturePeriods.map(period => {
        console.log('Processing period:', period);
        let processedFrequencyValue;

        switch (period.frequencyType) {
          case 'WEEKLY':
            const selectedBackendDays = (period.frequencyValue as number[] || [])
              .map(dayValue => {
                const dayObject = weekDays.find(d => d.value === dayValue);
                return dayObject ? dayObject.backendValue : null;
              })
              .filter(Boolean)
              .join(',');
            processedFrequencyValue = selectedBackendDays;
            break;
          case 'X_DAYS':
            processedFrequencyValue = String(period.frequencyValue || '1');
            break;
          case 'MONTHLY_SPECIFIC':
            processedFrequencyValue = (period.frequencyValue as string || '').split(',') 
              .map(d => d.trim())
              .filter(d => d && !isNaN(parseInt(d)) && parseInt(d) >= 1 && parseInt(d) <= 31)
              .join(',');
            break;
          default:
            processedFrequencyValue = '';
        }

        return {
          start_date: period.startDate,
          end_date: period.endDate,
          frequency_type: period.frequencyType,
          frequency_value: processedFrequencyValue,
          reference_date: period.frequencyType === 'X_DAYS' ? period.referenceDate : undefined,
          is_active: period.isActive,
        };
      });
    }

    console.log('Final convict data:', convictData);
    console.log('Final periods data:', periodsData);
    
    onSubmit(convictData, periodsData);
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit, (errors) => {
          console.log('Form validation errors:', errors);
          console.log('Form state:', form.formState);
        })} className="space-y-8">
          {/* Convict Information Section */}
          <Card className="windows-card">
            <CardHeader>
              <CardTitle className="windows-section-title flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Hükümlü Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gradient-to-br from-white via-slate-50/80 to-blue-50/30 backdrop-blur-sm border border-slate-200/50 rounded-xl shadow-lg p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* TC Kimlik No */}
                  <FormField
                    control={form.control}
                    name="tcNo"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                          TC Kimlik No <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="11 haneli TC Kimlik No"
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      </FormItem>
                    )}
                  />

                  {/* Ad */}
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                          Ad <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Ad"
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      </FormItem>
                    )}
                  />

                  {/* Soyad */}
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                          Soyad <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Soyad"
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      </FormItem>
                    )}
                  />

                  {/* Telefon Numarası */}
                  <FormField
                    control={form.control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                          Hükümlü Telefon Numarası
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="0555 123 45 67"
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      </FormItem>
                    )}
                  />

                  {/* Yakınına Ait Telefon Numarası */}
                  <FormField
                    control={form.control}
                    name="relativePhoneNumber"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                          Yakınına Ait Telefon Numarası
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="0555 123 45 67"
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      </FormItem>
                    )}
                  />

                  {/* Denetim Başlangıç Tarihi */}
                  <FormField
                    control={form.control}
                    name="supervisionStartDate"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                          Denetim Başlangıç Tarihi <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      </FormItem>
                    )}
                  />

                  {/* Denetim Bitiş Tarihi */}
                  <FormField
                    control={form.control}
                    name="supervisionEndDate"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                          Denetim Bitiş Tarihi <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            disabled={isLoading}
                            min={supervisionStartDate}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                        <p className="text-xs text-slate-500 mt-1">Başlangıç tarihinden sonra olmalıdır</p>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Adres */}
                <div className="mt-6">
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">Adres Bilgileri</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Tam adres bilgilerini buraya yazın..."
                            rows={3}
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Dosya Numarası */}
                <div className="mt-6">
                  <FormField
                    control={form.control}
                    name="fileNumber"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">Dosya Numarası</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Örn: 2025/34 NKL"
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                        <p className="text-xs text-slate-500 mt-1">Hükümlünün dava dosya numarası</p>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Notlar */}
                <div className="mt-6">
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">Notlar</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Hükümlü ile ilgili isteğe bağlı notlar..."
                            rows={4}
                            disabled={isLoading}
                            className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                        <p className="text-xs text-slate-500 mt-1">İsteğe bağlı - ek bilgiler ekleyebilirsiniz</p>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Signature Periods Section */}
          <Card className="windows-card">
            <CardHeader>
              <CardTitle className="windows-section-title flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                  İmza Periyotları
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showSignaturePeriods}
                    onCheckedChange={setShowSignaturePeriods}
                    disabled={isLoading}
                  />
                  <span className="text-sm text-slate-600">İmza periyodu ekle</span>
                </div>
              </CardTitle>
            </CardHeader>
            
            {showSignaturePeriods && (
              <CardContent>
                {(!supervisionStartDate || !supervisionEndDate) && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">ℹ️</span>
                        </div>
                      </div>
                      <div>
                        <h4 className="text-blue-800 font-medium">İmza Periyodu Ekleme</h4>
                        <p className="text-blue-700 text-sm mt-1">
                          İmza periyotları eklemek için önce yukarıda <strong>Denetim Başlangıç</strong> ve <strong>Denetim Bitiş</strong> tarihlerini girmeniz gerekmektedir.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                <div className="space-y-6">
                  {fields.length === 0 && (
                    <div className="text-center py-8 text-slate-500">
                      <p>Henüz hiç imza periyodu eklenmemiş.</p>
                      <p className="text-sm mt-1">Yeni bir periyot eklemek için aşağıdaki butona tıklayın.</p>
                    </div>
                  )}

                  {fields.map((field, index) => (
                    <div key={field.id} className="bg-gradient-to-br from-white via-slate-50/80 to-blue-50/30 backdrop-blur-sm border border-slate-200/50 rounded-xl shadow-lg p-6">
                      <div className="flex items-center justify-between mb-6">
                        <h4 className="text-lg font-semibold text-slate-800">İmza Periyodu {index + 1}</h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                          disabled={isLoading}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Start Date */}
                        <FormField
                          control={form.control}
                          name={`signaturePeriods.${index}.startDate`}
                          render={({ field }) => (
                            <FormItem className="space-y-3">
                              <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                                Periyot Başlangıç Tarihi <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="date"
                                  disabled={isLoading}
                                  min={supervisionStartDate}
                                  max={supervisionEndDate}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                            </FormItem>
                          )}
                        />

                        {/* End Date */}
                        <FormField
                          control={form.control}
                          name={`signaturePeriods.${index}.endDate`}
                          render={({ field }) => (
                            <FormItem className="space-y-3">
                              <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                                Periyot Bitiş Tarihi <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="date"
                                  disabled={isLoading}
                                  min={supervisionStartDate}
                                  max={supervisionEndDate}
                                  className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Frequency Type */}
                      <div className="mt-6">
                        <FormField
                          control={form.control}
                          name={`signaturePeriods.${index}.frequencyType`}
                          render={({ field }) => (
                            <FormItem className="space-y-3">
                              <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                                Sıklık Türü <span className="text-red-500">*</span>
                              </FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                                <FormControl>
                                  <SelectTrigger className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md">
                                    <SelectValue placeholder="Sıklık türünü seçin" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="WEEKLY">Haftalık (Haftanın günleri)</SelectItem>
                                  <SelectItem value="X_DAYS">X günde bir</SelectItem>
                                  <SelectItem value="MONTHLY_SPECIFIC">Ayın belirli günleri</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Frequency Value based on type */}
                      {form.watch(`signaturePeriods.${index}.frequencyType`) === 'WEEKLY' && (
                        <div className="mt-6">
                          <FormField
                            control={form.control}
                            name={`signaturePeriods.${index}.frequencyValue`}
                            render={() => (
                              <FormItem className="space-y-3">
                                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                                  Haftanın Günleri <span className="text-red-500">*</span>
                                </FormLabel>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                  {weekDays.map((day) => (
                                    <FormField
                                      key={day.value}
                                      control={form.control}
                                      name={`signaturePeriods.${index}.frequencyValue`}
                                      render={({ field }) => {
                                        const currentValue = field.value as number[] || [];
                                        return (
                                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                            <FormControl>
                                              <Checkbox
                                                checked={currentValue.includes(day.value)}
                                                onCheckedChange={(checked) => {
                                                  if (checked) {
                                                    field.onChange([...currentValue, day.value]);
                                                  } else {
                                                    field.onChange(currentValue.filter((value) => value !== day.value));
                                                  }
                                                }}
                                                disabled={isLoading}
                                                className="mt-1"
                                              />
                                            </FormControl>
                                            <FormLabel className="text-sm font-normal cursor-pointer">
                                              {day.label}
                                            </FormLabel>
                                          </FormItem>
                                        );
                                      }}
                                    />
                                  ))}
                                </div>
                                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                              </FormItem>
                            )}
                          />
                        </div>
                      )}

                      {form.watch(`signaturePeriods.${index}.frequencyType`) === 'X_DAYS' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                          <FormField
                            control={form.control}
                            name={`signaturePeriods.${index}.frequencyValue`}
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                                  Kaç günde bir <span className="text-red-500">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="1"
                                    max="365"
                                    placeholder="Gün sayısı"
                                    disabled={isLoading}
                                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`signaturePeriods.${index}.referenceDate`}
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                                  Referans Tarihi
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="date"
                                    disabled={isLoading}
                                    min={supervisionStartDate}
                                    max={supervisionEndDate}
                                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                                <p className="text-xs text-slate-500 mt-1">
                                  İlk imza tarihi olarak kullanılacak referans tarihi
                                </p>
                              </FormItem>
                            )}
                          />
                        </div>
                      )}

                      {form.watch(`signaturePeriods.${index}.frequencyType`) === 'MONTHLY_SPECIFIC' && (
                        <div className="mt-6">
                          <FormField
                            control={form.control}
                            name={`signaturePeriods.${index}.frequencyValue`}
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                                  Ayın Günleri <span className="text-red-500">*</span>
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Virgülle ayırarak yazın (örn: 1, 15, 30)"
                                    disabled={isLoading}
                                    className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                                <p className="text-xs text-slate-500 mt-1">
                                  1-31 arası günleri virgülle ayırarak yazın (örn: 1, 15, 30)
                                </p>
                              </FormItem>
                            )}
                          />
                        </div>
                      )}

                      {/* Active Status */}
                      <div className="mt-6">
                        <FormField
                          control={form.control}
                          name={`signaturePeriods.${index}.isActive`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                              <div className="space-y-1">
                                <FormLabel className="text-base font-semibold text-slate-800">Aktif Durumu</FormLabel>
                                <div className="text-sm text-slate-600">
                                  Bu periyodun aktif olup olmadığını belirler
                                </div>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                  className="data-[state=checked]:bg-green-600"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  ))}

                  {/* Add Period Button */}
                  <div className="flex flex-col items-center space-y-3">
                    {(!supervisionStartDate || !supervisionEndDate) && (
                      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 text-center">
                        <p className="text-amber-700 text-sm font-medium">
                          ⚠️ İmza periyodu eklemek için önce denetim başlangıç ve bitiş tarihlerini giriniz
                        </p>
                      </div>
                    )}
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addSignaturePeriod}
                      disabled={isLoading || !supervisionStartDate || !supervisionEndDate}
                      className="flex items-center gap-2 px-6 py-3 bg-white/80 border-2 border-blue-300 text-blue-600 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-blue-50 hover:border-blue-400 hover:shadow-md focus:ring-2 focus:ring-blue-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      <PlusIcon className="w-4 h-4" />
                      Yeni İmza Periyodu Ekle
                    </Button>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-8 border-t border-slate-200/70">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="px-8 py-3 bg-white/80 border-2 border-slate-300 text-slate-600 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-slate-50 hover:border-slate-400 hover:shadow-md focus:ring-2 focus:ring-slate-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              İptal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="px-8 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium rounded-xl shadow-md hover:shadow-lg focus:ring-2 focus:ring-green-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Kaydediliyor...
                </div>
              ) : (
                'Hükümlüyü ve İmza Periyotlarını Kaydet'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
