# File Number Field - Final End-to-End Test Plan

## Test Date: 4 Haziran 2025
## Application Status: Development server running on localhost:1420

## COMPLETED IMPLEMENTATION:
✅ Database schema with file_number column
✅ Rust backend CRUD operations updated
✅ TypeScript validation and schema
✅ Frontend forms updated with file number input
✅ Display column added to convict list
✅ Excel import/export functionality
✅ Basic database testing verified

## END-TO-END TESTING CHECKLIST:

### 1. CONVICT LIST PAGE - File Number Display
- [ ] Navigate to convict list page
- [ ] Verify "Dosya No" column is visible in table
- [ ] Check existing convicts show file numbers or "-" for empty
- [ ] Verify test convict with "2025/34 NKL" displays correctly

### 2. ADD NEW CONVICT - File Number Input
- [ ] Navigate to "Add New Convict" page
- [ ] Verify "Dosya Numarası" field is present in form
- [ ] Check placeholder text shows "Örn: 2025/34 NKL"
- [ ] Test adding convict WITHOUT file number (should work, optional field)
- [ ] Test adding convict WITH file number (e.g., "2025/100 NKL")
- [ ] Verify new convict appears in list with correct file number

### 3. EDIT EXISTING CONVICT - File Number Modification
- [ ] Navigate to edit page for existing convict
- [ ] Verify file number field shows current value (or empty)
- [ ] Test adding file number to convict that doesn't have one
- [ ] Test modifying existing file number
- [ ] Test clearing file number (should save as empty)
- [ ] Verify changes persist in convict list

### 4. ADD CONVICT WITH PERIODS - File Number Integration
- [ ] Navigate to "Add Convict with Periods" page
- [ ] Verify file number field is present and functional
- [ ] Test complete workflow with file number
- [ ] Verify convict and periods are created with file number

### 5. EXCEL IMPORT - File Number Processing
- [ ] Navigate to Excel import page
- [ ] Download the import template
- [ ] Verify "Dosya Numarası" column exists in template
- [ ] Check example values are present (2025/1 NKL, etc.)
- [ ] Create test Excel file with file numbers
- [ ] Import test file and verify file numbers are processed
- [ ] Check imported convicts show in list with correct file numbers

### 6. EXCEL EXPORT - File Number Inclusion
- [ ] Navigate to Excel export page
- [ ] Export convict data to Excel
- [ ] Open exported file and verify "Dosya Numarası" column exists
- [ ] Check file numbers are correctly exported
- [ ] Verify empty file numbers show as blank cells

### 7. DATA PERSISTENCE TESTING
- [ ] Add several convicts with different file number patterns
- [ ] Close and restart application
- [ ] Verify all file numbers persist after restart
- [ ] Test search/filter functionality with file numbers

### 8. VALIDATION TESTING
- [ ] Test very long file numbers (boundary testing)
- [ ] Test special characters in file numbers
- [ ] Test Turkish characters in file numbers
- [ ] Verify form submission works with various formats

## TEST DATA EXAMPLES:
- "2025/1 NKL"
- "2025/34 KRY" 
- "2024/156 ASL"
- "2025/999 İZM"
- "2023/5678 ANK"

## SUCCESS CRITERIA:
- All forms accept file number input correctly
- File numbers display in convict list
- Excel import/export handles file numbers
- Data persists across application restarts
- No errors or crashes during any operations
- Optional field behavior works (can be empty)

## NOTES:
- File number is optional field (can be empty)
- Should support Turkish characters
- Format examples: "2025/34 NKL", "2024/156 KRY"
- Field appears as "Dosya Numarası" in Turkish UI
