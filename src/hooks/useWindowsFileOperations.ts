import { useState, useCallback } from 'react';
import { save, open } from '@tauri-apps/plugin-dialog';
import { writeTextFile, readTextFile } from '@tauri-apps/plugin-fs';

interface UseWindowsFileOperationsOptions {
  defaultPath?: string;
  filters?: { name: string; extensions: string[] }[];
}

export function useWindowsFileOperations(options: UseWindowsFileOperationsOptions = {}) {
  const [lastFilePath, setLastFilePath] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const {
    defaultPath,
    filters = []
  } = options;

  /**
   * Save content to a file with a Windows-style save dialog
   */
  const saveFile = useCallback(async (content: string, suggestedFileName?: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Show the save dialog
      const filePath = await save({
        defaultPath: suggestedFileName || defaultPath,
        filters: filters.length ? filters : [
          { name: 'All Files', extensions: ['*'] }
        ]
      });
      
      if (!filePath) {
        // User canceled the dialog
        setLoading(false);
        return null;
      }
      
      // Save the file (filePath from save dialog is already absolute, no baseDir needed)
      await writeTextFile(filePath, content);
      setLastFilePath(filePath);
      setLoading(false);
      return filePath;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save file');
      setLoading(false);
      return null;
    }
  }, [defaultPath, filters]);

  /**
   * Open a file with a Windows-style open dialog
   */
  const openFile = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Show the open dialog
      const selected = await open({
        multiple: false,
        filters: filters.length ? filters : [
          { name: 'All Files', extensions: ['*'] }
        ]
      });
      
      if (!selected) {
        // User canceled the dialog
        setLoading(false);
        return null;
      }
      
      const filePath = selected as string;
      
      // Read the file (filePath from open dialog is already absolute, no baseDir needed)
      const content = await readTextFile(filePath);
      setLastFilePath(filePath);
      setLoading(false);
      return { path: filePath, content };
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to open file');
      setLoading(false);
      return null;
    }
  }, [filters]);
  
  /**
   * Export data to a CSV file
   */
  const exportToCsv = useCallback(async (data: Record<string, string | number | boolean | null | undefined>[], filename = 'export.csv') => {
    setLoading(true);
    setError(null);
    
    try {
      if (!data.length) {
        throw new Error('No data to export');
      }
      
      // Get headers from first row
      const headers = Object.keys(data[0]);
      
      // Convert data to CSV
      const csvRows = [
        headers.join(','), // Header row
        ...data.map(row => headers.map(header => {
          const cell = row[header];
          // Handle null values and escape commas and quotes
          const value = cell === null || cell === undefined ? '' : String(cell);
          return `"${value.replace(/"/g, '""')}"`;
        }).join(','))
      ];
      
      const csvContent = csvRows.join('\n');
      
      // Save to file
      const filePath = await save({
        defaultPath: filename,
        filters: [
          { name: 'CSV', extensions: ['csv'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });
      
      if (!filePath) {
        // User canceled
        setLoading(false);
        return null;
      }
      
      // Save the file (filePath from save dialog is already absolute, no baseDir needed)
      await writeTextFile(filePath, csvContent);
      setLastFilePath(filePath);
      setLoading(false);
      return filePath;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export CSV');
      setLoading(false);
      return null;
    }
  }, []);
  
  /**
   * Print a document using the system print dialog
   */
  const printContent = useCallback((contentId: string) => {
    try {
      const element = document.getElementById(contentId);
      if (!element) {
        throw new Error(`Element with id "${contentId}" not found`);
      }
      
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Failed to open print window');
      }
      
      // Write content to the new window
      printWindow.document.write(`
        <html>
          <head>
            <title>Print</title>
            <style>
              body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 12px; }
              table { border-collapse: collapse; width: 100%; }
              th, td { border: 1px solid #ccc; padding: 4px 8px; text-align: left; }
              th { background-color: #f0f0f0; }
              @media print {
                body { margin: 0; padding: 15mm; }
              }
            </style>
          </head>
          <body>
            ${element.outerHTML}
          </body>
        </html>
      `);
      
      // Print and close
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to print');
      return false;
    }
  }, []);
  
  return {
    saveFile,
    openFile,
    exportToCsv,
    printContent,
    lastFilePath,
    loading,
    error
  };
}
