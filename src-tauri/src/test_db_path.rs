// Test script to verify Rust backend database path
use std::process::Command;

fn main() {
    println!("🔍 Testing database path resolution...");
    
    // Simulate the database path logic
    let mut path = std::env::current_dir().unwrap();
    println!("Current directory: {:?}", path);
    
    // If we're in src-tauri directory, go up one level
    if path.file_name().and_then(|s| s.to_str()) == Some("src-tauri") {
        path.pop();
        println!("Adjusted to parent directory: {:?}", path);
    }
    
    path.push("database.sqlite");
    println!("Database path: {:?}", path);
    
    // Check if database exists
    if path.exists() {
        println!("✅ Database file found!");
        
        // Check file size
        if let Ok(metadata) = std::fs::metadata(&path) {
            println!("📊 Database size: {} bytes", metadata.len());
        }
    } else {
        println!("❌ Database file not found!");
        println!("Please run: npm run db:migrate");
    }
}
