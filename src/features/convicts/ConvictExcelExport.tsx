import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import * as XLSX from 'xlsx';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { getConvictFullDetails, saveBinaryFile } from '@/lib/tauri-api';
import { save } from '@tauri-apps/plugin-dialog';
import { DocumentArrowDownIcon } from '@heroicons/react/24/outline';
import type { ConvictFullDetails } from '@/shared/schema';
import { groupSignaturePeriods } from '@/lib/signature-dates';

interface ConvictExcelExportProps {
  disabled?: boolean;
}

export default function ConvictExcelExport({ disabled = false }: ConvictExcelExportProps) {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const exportMutation = useMutation({
    mutationFn: async () => {
      setIsExporting(true);
      
      try {
        // Fetch all convicts with their full details
        const convictsData = await getConvictFullDetails();
        
        if (!convictsData || convictsData.length === 0) {
          throw new Error('Dışa aktarılacak hükümlü verisi bulunamadı.');
        }

        // Prepare Excel data
        const excelData = await prepareExcelData(convictsData);
        
        // Create workbook and worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.aoa_to_sheet(excelData);

        // Set column widths for better readability - Import ile uyumlu genişlikler
        const colWidths = [
          // Hükümlü temel bilgileri
          { wch: 15 }, // TC Kimlik No
          { wch: 12 }, // Ad
          { wch: 12 }, // Soyad
          { wch: 20 }, // Hükümlü Telefon Numarası
          { wch: 22 }, // Yakınına Ait Telefon Numarası
          { wch: 40 }, // Adres Bilgileri
          { wch: 16 }, // Dosya Numarası
          { wch: 22 }, // Denetim Başlangıç Tarihi
          { wch: 22 }, // Denetim Bitiş Tarihi
          { wch: 8 },  // Aktif
          { wch: 30 }, // Notlar
          // İmza periyodu bilgileri
          { wch: 22 }, // İmza Periyodu Başlangıç
          { wch: 22 }, // İmza Periyodu Bitiş
          { wch: 18 }, // İmza Sıklığı Türü
          { wch: 25 }, // İmza Sıklığı Değeri
          { wch: 20 }, // İmza Referans Tarihi
          { wch: 18 }, // İmza Periyodu Aktif
          // Zaman kısıtlamaları
          { wch: 18 }, // İmza Başlangıç Saati
          { wch: 18 }, // İmza Bitiş Saati
          { wch: 30 }, // İzin Verilen Günler
          // Muafiyet bilgileri
          { wch: 16 }, // Muafiyet Türü
          { wch: 20 }, // Muafiyet Başlangıç
          { wch: 20 }, // Muafiyet Bitiş
          { wch: 30 }, // Muafiyet Açıklaması
          { wch: 25 }, // Muafiyet Belgesi
          { wch: 16 }, // Muafiyet Aktif
        ];
        worksheet['!cols'] = colWidths;

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Hükümlü Listesi');

        // Convert to binary
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

        // Show save dialog
        const currentDate = new Date().toISOString().split('T')[0];
        const filePath = await save({
          defaultPath: `hukumlu-listesi-${currentDate}.xlsx`,
          filters: [
            { name: 'Excel Dosyaları', extensions: ['xlsx'] },
            { name: 'Tüm Dosyalar', extensions: ['*'] }
          ]
        });

        if (!filePath) {
          // User canceled
          return;
        }

        // Save the binary file
        await saveBinaryFile(filePath, new Uint8Array(excelBuffer));

        toast({
          title: 'Dışa Aktarma Başarılı',
          description: `${convictsData.length} hükümlü verisi Excel dosyasına aktarıldı.`,
        });

      } catch (error) {
        console.error('Export error:', error);
        toast({
          title: 'Dışa Aktarma Hatası',
          description: error instanceof Error ? error.message : 'Excel dosyası oluşturulurken bir hata oluştu.',
          variant: 'destructive',
        });
      } finally {
        setIsExporting(false);
      }
    },
    onError: (error) => {
      console.error('Export mutation error:', error);
      toast({
        title: 'Dışa Aktarma Hatası',
        description: 'Veriler dışa aktarılırken bir hata oluştu.',
        variant: 'destructive',
      });
      setIsExporting(false);
    }
  });

  const prepareExcelData = async (convictsData: ConvictFullDetails[]): Promise<(string | number | boolean)[][]> => {
    // Header row - exact same format as import template
    const headers = [
      'TC Kimlik No', 'Ad', 'Soyad', 'Hükümlü Telefon Numarası', 'Yakınına Ait Telefon Numarası', 'Adres Bilgileri', 'Dosya Numarası', 'Denetim Başlangıç Tarihi', 'Denetim Bitiş Tarihi', 'Aktif', 'Notlar',
      // İmza periyodu sütunları
      'İmza Periyodu Başlangıç', 'İmza Periyodu Bitiş', 'İmza Sıklığı Türü', 'İmza Sıklığı Değeri', 'İmza Referans Tarihi', 'İmza Periyodu Aktif',
      // Zaman kısıtlamaları sütunları
      'İmza Başlangıç Saati', 'İmza Bitiş Saati', 'İzin Verilen Günler',
      // Muafiyet sütunları
      'Muafiyet Türü', 'Muafiyet Başlangıç', 'Muafiyet Bitiş', 'Muafiyet Açıklaması', 'Muafiyet Belgesi', 'Muafiyet Aktif'
    ];

    const data: (string | number | boolean)[][] = [headers];

    for (const convict of convictsData) {
      // Group signature periods if they exist
      // Note: We need to extend the signature_periods type to work with groupSignaturePeriods
      const extendedSignaturePeriods = convict.signature_periods?.map(period => ({
        ...period,
        convict_id: convict.id,
        created_at: null,
        time_start: null,
        time_end: null,
        allowed_days: null,
        frequency_type: period.frequency_type as 'WEEKLY' | 'X_DAYS' | 'MONTHLY_SPECIFIC',
        reference_date: period.reference_date || null,
      })) || [];

      const groupedSignaturePeriods = extendedSignaturePeriods.length > 0
        ? groupSignaturePeriods(extendedSignaturePeriods)
        : [];

      // If convict has no signature periods or exemptions, create a single row
      if (groupedSignaturePeriods.length === 0 && 
          (!convict.exemptions || convict.exemptions.length === 0)) {
        data.push(createConvictRow(convict, null, null));
      } else {
        // Create rows for each combination of signature period and exemption
        // If we have both signature periods and exemptions, create combinations
        if (groupedSignaturePeriods.length && convict.exemptions?.length) {
          for (const period of groupedSignaturePeriods) {
            for (const exemption of convict.exemptions) {
              data.push(createConvictRow(convict, period, exemption));
            }
          }
        } else if (groupedSignaturePeriods.length) {
          // Only signature periods
          for (const period of groupedSignaturePeriods) {
            data.push(createConvictRow(convict, period, null));
          }
        } else if (convict.exemptions?.length) {
          // Only exemptions
          for (const exemption of convict.exemptions) {
            data.push(createConvictRow(convict, null, exemption));
          }
        }
      }
    }

    return data;
  };

  const createConvictRow = (
    convict: ConvictFullDetails, 
    signaturePeriod: ReturnType<typeof groupSignaturePeriods>[0] | null, 
    exemption: { id: number; exemption_type: string; start_date: string; end_date: string; description?: string; document_path?: string; is_active: boolean; } | null
  ): (string | number | boolean)[] => {
    return [
      // Convict basic info
      convict.tc_no || '',
      convict.first_name || '',
      convict.last_name || '',
      convict.phone_number || '',
      convict.relative_phone_number || '',
      convict.address || '',
      convict.file_number || '',
      convict.supervision_start_date || '',
      convict.supervision_end_date || '',
      convict.is_active ? 'Evet' : 'Hayır',
      convict.notes || '',
      
      // Signature period info
      signaturePeriod?.start_date || '',
      signaturePeriod?.end_date || '',
      signaturePeriod ? convertFrequencyTypeToTurkish(signaturePeriod.frequency_type) : '',
      signaturePeriod ? convertFrequencyValueToTurkish(signaturePeriod.frequency_type, signaturePeriod.frequency_value) : '',
      signaturePeriod?.reference_date || '',
      signaturePeriod ? (signaturePeriod.is_active ? 'Evet' : 'Hayır') : '',
      
      // Time restrictions info
      signaturePeriod?.time_start || '',
      signaturePeriod?.time_end || '',
      signaturePeriod ? convertAllowedDaysToTurkish(signaturePeriod.allowed_days ? JSON.parse(signaturePeriod.allowed_days) : null) : '',
      
      // Exemption info
      exemption ? convertExemptionTypeToTurkish(exemption.exemption_type) : '',
      exemption?.start_date || '',
      exemption?.end_date || '',
      exemption?.description || '',
      exemption?.document_path || '',
      exemption ? (exemption.is_active ? 'Evet' : 'Hayır') : ''
    ];
  };

  const convertFrequencyTypeToTurkish = (frequencyType: string): string => {
    const mapping: { [key: string]: string } = {
      'WEEKLY': 'HAFTALIK',
      'X_DAYS': 'GÜNLÜK',
      'MONTHLY_SPECIFIC': 'AYLIK'
    };
    return mapping[frequencyType] || frequencyType;
  };

  const convertFrequencyValueToTurkish = (frequencyType: string, frequencyValue: string): string => {
    if (frequencyType === 'WEEKLY') {
      const weekdayMapping: { [key: string]: string } = {
        'MONDAY': 'PAZARTESİ',
        'TUESDAY': 'SALI',
        'WEDNESDAY': 'ÇARŞAMBA',
        'THURSDAY': 'PERŞEMBE',
        'FRIDAY': 'CUMA',
        'SATURDAY': 'CUMARTESİ',
        'SUNDAY': 'PAZAR'
      };
      
      // Handle grouped frequency values (comma-separated)
      if (frequencyValue && frequencyValue.includes(',')) {
        return frequencyValue
          .split(',')
          .map(day => weekdayMapping[day.trim().toUpperCase()] || day.trim())
          .join(', ');
      }
      
      return weekdayMapping[frequencyValue?.toUpperCase()] || frequencyValue;
    }
    return frequencyValue;
  };

  const convertExemptionTypeToTurkish = (exemptionType: string): string => {
    const mapping: { [key: string]: string } = {
      'LEAVE': 'İZİN',
      'MEDICAL_REPORT': 'SAĞLIK RAPORU'
    };
    return mapping[exemptionType] || exemptionType;
  };

  const convertAllowedDaysToTurkish = (allowedDays: string[] | null | undefined): string => {
    if (!allowedDays || !Array.isArray(allowedDays) || allowedDays.length === 0) return '';
    
    const dayMapping: { [key: string]: string } = {
      'MONDAY': 'Pazartesi',
      'TUESDAY': 'Salı', 
      'WEDNESDAY': 'Çarşamba',
      'THURSDAY': 'Perşembe',
      'FRIDAY': 'Cuma',
      'SATURDAY': 'Cumartesi',
      'SUNDAY': 'Pazar'
    };
    
    return allowedDays
      .map(day => dayMapping[day?.toUpperCase()] || day)
      .join(', ');
  };

  const handleExport = () => {
    exportMutation.mutate();
  };

  return (
    <Button 
      variant="outline" 
      size="sm"
      onClick={handleExport}
      disabled={disabled || isExporting}
    >
      <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
      {isExporting ? 'Dışa Aktarılıyor...' : 'Excel\'e Dışa Aktar'}
    </Button>
  );
}
