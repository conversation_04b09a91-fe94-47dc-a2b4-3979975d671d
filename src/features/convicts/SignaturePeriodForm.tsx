import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import type { ConvictWithDetails, InsertSignaturePeriod } from '@shared/schema';

const signaturePeriodSchema = z.object({
  startDate: z.string().min(1, 'Başlangıç tarihi gereklidir'),
  endDate: z.string().min(1, 'Bitiş tarihi gereklidir'),
  frequencyType: z.enum(['WEEKLY', 'X_DAYS', 'MONTHLY_SPECIFIC'], {
    required_error: '<PERSON>ıklık türü seçiniz',
  }),
  frequencyValue: z.any(),
  referenceDate: z.string().optional(),
  timeStart: z.string().optional(),
  timeEnd: z.string().optional(),
  allowedDays: z.array(z.string()).optional(),
  isActive: z.boolean().default(true),
}).refine(
  (data) => new Date(data.endDate) > new Date(data.startDate),
  {
    message: 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır',
    path: ['endDate'],
  }
).refine(
  (data) => {
    if (data.timeStart && data.timeEnd) {
      return data.timeEnd > data.timeStart;
    }
    return true;
  },
  {
    message: 'Bitiş saati başlangıç saatinden sonra olmalıdır',
    path: ['timeEnd'],
  }
);

type SignaturePeriodFormData = z.infer<typeof signaturePeriodSchema>;

interface SignaturePeriodFormProps {
  convict: ConvictWithDetails;
  initialData?: Partial<SignaturePeriodFormData>;
  onSubmit: (data: Omit<InsertSignaturePeriod, 'convict_id'>) => void;
  onCancel: () => void;
  isLoading?: boolean;
  submitLabel?: string;
}

const weekDays = [
  { value: 'MONDAY', label: 'Pazartesi' },
  { value: 'TUESDAY', label: 'Salı' },
  { value: 'WEDNESDAY', label: 'Çarşamba' },
  { value: 'THURSDAY', label: 'Perşembe' },
  { value: 'FRIDAY', label: 'Cuma' },
  { value: 'SATURDAY', label: 'Cumartesi' },
  { value: 'SUNDAY', label: 'Pazar' },
];

const frequencyWeekDays = [
  { value: 0, label: 'Pazartesi', backendValue: 'MONDAY' },
  { value: 1, label: 'Salı', backendValue: 'TUESDAY' },
  { value: 2, label: 'Çarşamba', backendValue: 'WEDNESDAY' },
  { value: 3, label: 'Perşembe', backendValue: 'THURSDAY' },
  { value: 4, label: 'Cuma', backendValue: 'FRIDAY' },
  { value: 5, label: 'Cumartesi', backendValue: 'SATURDAY' },
  { value: 6, label: 'Pazar', backendValue: 'SUNDAY' },
];

export default function SignaturePeriodForm({
  convict,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  submitLabel = 'Kaydet',
}: SignaturePeriodFormProps) {
  const form = useForm<SignaturePeriodFormData>({
    resolver: zodResolver(signaturePeriodSchema),
    defaultValues: {
      startDate: '',
      endDate: '',
      frequencyType: 'WEEKLY',
      frequencyValue: [],
      referenceDate: '',
      timeStart: '',
      timeEnd: '',
      allowedDays: [],
      isActive: true,
      ...initialData,
    },
  });

  const frequencyType = form.watch('frequencyType');

  const handleSubmit = (data: SignaturePeriodFormData) => {
    let processedFrequencyValue;

    switch (data.frequencyType) {
      case 'WEEKLY':
        const selectedBackendDays = (data.frequencyValue as number[] || [])
          .map(dayValue => {
            const dayObject = frequencyWeekDays.find(d => d.value === dayValue);
            return dayObject ? dayObject.backendValue : null;
          })
          .filter(Boolean)
          .join(',');
        processedFrequencyValue = selectedBackendDays;
        break;
      case 'X_DAYS':
        processedFrequencyValue = String(data.frequencyValue || '1');
        break;
      case 'MONTHLY_SPECIFIC':
        processedFrequencyValue = (data.frequencyValue as string || '').split(',') 
          .map(d => d.trim())
          .filter(d => d && !isNaN(parseInt(d)) && parseInt(d) >= 1 && parseInt(d) <= 31)
          .join(',');
        break;
      default:
        processedFrequencyValue = '';
    }

    const submitData: Omit<InsertSignaturePeriod, 'convict_id'> = {
      start_date: data.startDate,
      end_date: data.endDate,
      frequency_type: data.frequencyType,
      frequency_value: processedFrequencyValue,
      reference_date: data.frequencyType === 'X_DAYS' ? data.referenceDate : undefined,
      time_start: data.timeStart || undefined,
      time_end: data.timeEnd || undefined,
      allowed_days: data.allowedDays && data.allowedDays.length > 0 ? JSON.stringify(data.allowedDays) : undefined,
      is_active: data.isActive,
    };

    onSubmit(submitData);
  };

  return (
    <div className="bg-gradient-to-br from-white via-slate-50/80 to-blue-50/30 backdrop-blur-sm border border-slate-200/50 rounded-xl shadow-lg p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Periyot Başlangıç Tarihi <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      disabled={isLoading}
                      min={convict.supervision_start_date}
                      max={convict.supervision_end_date}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  <p className="text-xs text-slate-500 mt-1">
                    Denetim süresi: {convict.supervision_start_date} - {convict.supervision_end_date}
                  </p>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Periyot Bitiş Tarihi <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      disabled={isLoading}
                      min={convict.supervision_start_date}
                      max={convict.supervision_end_date}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                </FormItem>
              )}
            />
          </div>

          {/* Frequency Type */}
          <FormField
            control={form.control}
            name="frequencyType"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                  Sıklık Türü <span className="text-red-500">*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                  <FormControl>
                    <SelectTrigger className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md">
                      <SelectValue placeholder="Sıklık türünü seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="border border-slate-200 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg">
                    <SelectItem value="WEEKLY" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        Haftalık
                      </div>
                    </SelectItem>
                    <SelectItem value="X_DAYS" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        X Günde Bir
                      </div>
                    </SelectItem>
                    <SelectItem value="MONTHLY_SPECIFIC" className="hover:bg-blue-50 focus:bg-blue-50 rounded-lg m-1">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        Aylık Belirli Günler
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
              </FormItem>
            )}
          />

          {/* Frequency Value based on type */}
          {frequencyType === 'WEEKLY' && (
            <FormField
              control={form.control}
              name="frequencyValue"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Haftanın Günleri <span className="text-red-500">*</span>
                  </FormLabel>
                  <Card className="border border-slate-200 bg-white/60 backdrop-blur-sm rounded-xl shadow-sm">
                    <CardContent className="p-4">
                      <div className="grid grid-cols-2 md:grid-cols-7 gap-2">
                        {frequencyWeekDays.map((day) => (
                          <div key={day.value} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-blue-50/50 transition-colors">
                            <Checkbox
                              id={`day-${day.value}`}
                              checked={(field.value || []).includes(day.value)}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || [];
                                if (checked) {
                                  field.onChange([...currentValue, day.value]);
                                } else {
                                  field.onChange(currentValue.filter((v: number) => v !== day.value));
                                }
                              }}
                              disabled={isLoading}
                              className="border-2 border-slate-300 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                            />
                            <label
                              htmlFor={`day-${day.value}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                            >
                              {day.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  <p className="text-xs text-slate-500 mt-1">En az bir gün seçmelisiniz</p>
                </FormItem>
              )}
            />
          )}

          {frequencyType === 'X_DAYS' && (
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="frequencyValue"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                      Gün Aralığı <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="30"
                        placeholder="Kaç günde bir (örn: 3)"
                        disabled={isLoading}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || '')}
                        className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      />
                    </FormControl>
                    <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                    <p className="text-xs text-slate-500 mt-1">1-30 arası bir değer girin</p>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="referenceDate"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                      Referans Tarihi <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        disabled={isLoading}
                        className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                    <p className="text-xs text-slate-500 mt-1">
                      İlk imza tarihi olarak kullanılacak referans tarihi
                    </p>
                  </FormItem>
                )}
              />
            </div>
          )}

          {frequencyType === 'MONTHLY_SPECIFIC' && (
            <FormField
              control={form.control}
              name="frequencyValue"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                    Ayın Günleri <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Virgülle ayırarak yazın (örn: 1, 15, 30)"
                      disabled={isLoading}
                      className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-blue-500/30 focus:border-blue-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                  <p className="text-xs text-slate-500 mt-1">
                    1-31 arası günleri virgülle ayırarak yazın (örn: 1, 15, 30)
                  </p>
                </FormItem>
              )}
            />
          )}

          {/* Time Restrictions Section */}
          <div className="space-y-4">
            <div className="border-t border-slate-200/70 pt-6">
              <h3 className="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                <div className="w-5 h-5 bg-gradient-to-br from-orange-500 to-red-500 rounded-full"></div>
                Zaman Kısıtlamaları (İsteğe Bağlı)
              </h3>
              
              {/* Time Range */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormField
                  control={form.control}
                  name="timeStart"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                        Başlangıç Saati
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="time"
                          disabled={isLoading}
                          className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-orange-500/30 focus:border-orange-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      <p className="text-xs text-slate-500 mt-1">
                        İmza atılabilecek en erken saat (örn: 10:00)
                      </p>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="timeEnd"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                        Bitiş Saati
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="time"
                          disabled={isLoading}
                          className="w-full px-4 py-3 border border-slate-200 bg-white/80 text-slate-700 rounded-xl shadow-sm backdrop-blur-sm focus:ring-2 focus:ring-orange-500/30 focus:border-orange-400 focus:bg-white disabled:bg-slate-50 disabled:opacity-60 transition-all duration-200 hover:border-slate-300 hover:shadow-md"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                      <p className="text-xs text-slate-500 mt-1">
                        İmza atılabilecek en geç saat (örn: 22:00)
                      </p>
                    </FormItem>
                  )}
                />
              </div>

              {/* Allowed Days */}
              <FormField
                control={form.control}
                name="allowedDays"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-semibold text-slate-700 tracking-wide">
                      İzin Verilen Günler
                    </FormLabel>
                    <Card className="border border-slate-200 bg-white/60 backdrop-blur-sm rounded-xl shadow-sm">
                      <CardContent className="p-4">
                        <div className="grid grid-cols-2 md:grid-cols-7 gap-2">
                          {weekDays.map((day) => (
                            <div key={day.value} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-orange-50/50 transition-colors">
                              <Checkbox
                                id={`allowed-day-${day.value}`}
                                checked={(field.value || []).includes(day.value)}
                                onCheckedChange={(checked) => {
                                  const currentValue = field.value || [];
                                  if (checked) {
                                    field.onChange([...currentValue, day.value]);
                                  } else {
                                    field.onChange(currentValue.filter((v: string) => v !== day.value));
                                  }
                                }}
                                disabled={isLoading}
                                className="border-2 border-slate-300 data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600"
                              />
                              <label
                                htmlFor={`allowed-day-${day.value}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                              >
                                {day.label}
                              </label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                    <FormMessage className="text-sm text-red-600 font-medium flex items-center gap-2" />
                    <p className="text-xs text-slate-500 mt-1">
                      Hiçbir gün seçilmezse tüm günlerde imza atılabilir
                    </p>
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Active Status */}
          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm p-4 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="space-y-1">
                  <FormLabel className="text-base font-semibold text-slate-800">Aktif Durumu</FormLabel>
                  <div className="text-sm text-slate-600">
                    Bu periyodun aktif olup olmadığını belirler
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isLoading}
                    className="data-[state=checked]:bg-green-600"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-slate-200/70">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="px-6 py-2.5 bg-white/80 border-2 border-slate-300 text-slate-600 font-medium rounded-xl shadow-sm backdrop-blur-sm hover:bg-slate-50 hover:border-slate-400 hover:shadow-md focus:ring-2 focus:ring-slate-300/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              İptal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl shadow-md hover:shadow-lg focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Kaydediliyor...
                </div>
              ) : (
                submitLabel
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
