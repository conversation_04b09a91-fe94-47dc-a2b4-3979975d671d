import { Link, useLocation } from 'wouter';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/store/authStore';
import {
  HomeIcon,
  UsersIcon,
  PencilIcon,
  DocumentChartBarIcon,
  UserGroupIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  CheckCircleIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { useState } from 'react';

const navigation = [
  { name: '<PERSON>', href: '/dashboard', icon: HomeIcon },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ö<PERSON>im<PERSON>',
    icon: UsersIcon,
    children: [
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/convicts' },
      { name: '<PERSON><PERSON>', href: '/convicts/new' },
    ],
  },
  { name: '<PERSON><PERSON><PERSON>', href: '/signatures/record', icon: PencilIcon },
  {
    name: '<PERSON><PERSON><PERSON>',
    icon: DocumentChartBarIcon,
    children: [
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/reports/daily' },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/reports/convict-history' },
      { name: '<PERSON><PERSON><PERSON>oru', href: '/reports/violations' },
    ],
  },
];

const adminNavigation = [
  { name: 'Kullanıcı Yönetimi', href: '/admin/users', icon: UserGroupIcon },
  { name: 'Sistem Ayarları', href: '/admin/settings', icon: CogIcon },
];

export default function Sidebar() {
  const [location] = useLocation();
  const { user, logout } = useAuthStore();
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});

  const toggleMenu = (menuName: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuName]: !prev[menuName],
    }));
  };

  const isActive = (href: string) => {
    return location === href || location.startsWith(href + '/');
  };

  const isMenuActive = (children: { href: string }[]) => {
    return children.some(child => isActive(child.href));
  };

  const renderNavItem = (item: any) => {
    if (item.children) {
      const isMenuOpen = expandedMenus[item.name];
      const hasActiveChild = isMenuActive(item.children);

      return (
        <div key={item.name} className="space-y-1">
          <button
            onClick={() => toggleMenu(item.name)}
            className={cn(
              "flex items-center w-full px-4 py-3 text-sm font-medium rounded-xl group transition-all duration-200",
              hasActiveChild
                ? "text-white bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg"
                : "text-slate-300 hover:text-white hover:bg-slate-700/50"
            )}
          >
            <item.icon className="w-5 h-5 mr-3 flex-shrink-0" />
            <span className="flex-1 text-left">{item.name}</span>
            <ChevronDownIcon
              className={cn(
                "w-4 h-4 transition-transform duration-200",
                isMenuOpen ? "rotate-180" : ""
              )}
            />
          </button>
          {isMenuOpen && (
            <div className="ml-4 space-y-1 border-l-2 border-slate-700/50 pl-4">
              {item.children.map((child: any) => (
                <Link
                  key={child.href}
                  href={child.href}
                  className={cn(
                    "block px-4 py-2.5 text-sm rounded-lg transition-all duration-200",
                    isActive(child.href)
                      ? "text-white bg-blue-600/20 border-l-2 border-blue-400 font-medium"
                      : "text-slate-400 hover:text-white hover:bg-slate-700/30"
                  )}
                >
                  {child.name}
                </Link>
              ))}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          "flex items-center px-4 py-3 text-sm font-medium rounded-xl group transition-all duration-200",
          isActive(item.href)
            ? "text-white bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg"
            : "text-slate-300 hover:text-white hover:bg-slate-700/50"
        )}
      >
        <item.icon className="w-5 h-5 mr-3 flex-shrink-0" />
        <span>{item.name}</span>
      </Link>
    );
  };

  return (
    <div className="hidden md:flex md:w-64 md:flex-col">
      <div className="flex flex-col flex-grow bg-gradient-to-b from-slate-900 to-slate-800 shadow-xl">
        {/* Logo/Brand */}
        <div className="flex items-center flex-shrink-0 px-6 py-6 border-b border-slate-700/50">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <CheckCircleIcon className="w-6 h-6 text-white" />
            </div>
            <div className="ml-4">
              <h1 className="text-xl font-bold text-white">EİTS</h1>
              <p className="text-xs text-slate-400 mt-0.5">İmza Takip Sistemi</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map(renderNavItem)}

          {/* Admin Only Navigation */}
          {user?.role === 'ADMIN' && (
            <>
              <div className="pt-6 mt-6 border-t border-slate-700/50">
                <div className="px-3 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider">
                  Yönetim
                </div>
              </div>
              <div className="space-y-2">
                {adminNavigation.map(renderNavItem)}
              </div>
            </>
          )}
        </nav>

        {/* User info and logout */}
        <div className="flex-shrink-0 px-4 py-6 border-t border-slate-700/50">
          <div className="flex items-center p-3 rounded-xl bg-slate-800/50">
            <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center">
              <UsersIcon className="w-5 h-5 text-slate-300" />
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-white">{user?.username}</p>
              <p className="text-xs text-slate-400">
                {user?.role === 'ADMIN' ? 'Yönetici' : 'Memur'}
              </p>
            </div>
            <button
              onClick={logout}
              className="p-2 rounded-lg hover:bg-slate-700/50 transition-colors group"
              title="Çıkış Yap"
            >
              <ArrowRightOnRectangleIcon className="w-5 h-5 text-slate-400 group-hover:text-slate-300" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
