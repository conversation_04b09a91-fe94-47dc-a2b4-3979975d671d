#!/bin/bash

# File Number Field - Comprehensive End-to-End Test Script
# Date: 4 Haziran 2025
# Testing all file number functionality through database verification

echo "=== FILE NUMBER FIELD - COMPREHENSIVE TESTING ==="
echo "Date: $(date)"
echo "Testing database: database.sqlite"
echo ""

# Test database path
DB_PATH="/Users/<USER>/eits/database.sqlite"

echo "1. CURRENT DATABASE STATE:"
echo "================================"
sqlite3 "$DB_PATH" "SELECT id, first_name, last_name, file_number FROM convicts ORDER BY id;"
echo ""

echo "2. TESTING FILE NUMBER STORAGE:"
echo "================================"

# Test adding a new convict with file number
echo "Adding test convict with file number 2025/TEST1..."
sqlite3 "$DB_PATH" "
INSERT INTO convicts (first_name, last_name, file_number, tc_kimlik, birth_date, entry_date, release_date, conviction_type, sentence_duration, remaining_duration, address, phone, emergency_contact, is_active) 
VALUES ('Test', 'Convict1', '2025/TEST1', '12345678901', '1990-01-01', '2024-01-01', '2026-01-01', 'Test Crime', '2 years', '1 year', 'Test Address', '555-1234', 'Test Contact', 1);
"

# Test adding a convict without file number
echo "Adding test convict without file number..."
sqlite3 "$DB_PATH" "
INSERT INTO convicts (first_name, last_name, file_number, tc_kimlik, birth_date, entry_date, release_date, conviction_type, sentence_duration, remaining_duration, address, phone, emergency_contact, is_active) 
VALUES ('Test', 'Convict2', NULL, '12345678902', '1990-01-01', '2024-01-01', '2026-01-01', 'Test Crime', '2 years', '1 year', 'Test Address', '555-1235', 'Test Contact', 1);
"

# Test adding convict with Turkish characters in file number
echo "Adding test convict with Turkish characters in file number..."
sqlite3 "$DB_PATH" "
INSERT INTO convicts (first_name, last_name, file_number, tc_kimlik, birth_date, entry_date, release_date, conviction_type, sentence_duration, remaining_duration, address, phone, emergency_contact, is_active) 
VALUES ('Test', 'Convict3', '2025/156 İZM', '12345678903', '1990-01-01', '2024-01-01', '2026-01-01', 'Test Crime', '2 years', '1 year', 'Test Address', '555-1236', 'Test Contact', 1);
"

echo ""
echo "3. VERIFICATION - ALL CONVICTS WITH FILE NUMBERS:"
echo "================================================="
sqlite3 "$DB_PATH" "
SELECT 
    id,
    first_name || ' ' || last_name as name,
    CASE 
        WHEN file_number IS NULL OR file_number = '' THEN '(boş)'
        ELSE file_number 
    END as file_number
FROM convicts 
ORDER BY id;
"

echo ""
echo "4. TESTING FILE NUMBER UPDATES:"
echo "==============================="

# Get the ID of one of our test convicts
TEST_ID=$(sqlite3 "$DB_PATH" "SELECT id FROM convicts WHERE first_name = 'Test' AND last_name = 'Convict2' LIMIT 1;")

if [ ! -z "$TEST_ID" ]; then
    echo "Updating convict ID $TEST_ID to add file number..."
    sqlite3 "$DB_PATH" "UPDATE convicts SET file_number = '2025/UPDATED' WHERE id = $TEST_ID;"
    
    echo "Verification - Updated convict:"
    sqlite3 "$DB_PATH" "SELECT id, first_name, last_name, file_number FROM convicts WHERE id = $TEST_ID;"
fi

echo ""
echo "5. TESTING FILE NUMBER SEARCH:"
echo "=============================="
echo "Searching for convicts with file numbers containing '2025':"
sqlite3 "$DB_PATH" "
SELECT id, first_name, last_name, file_number 
FROM convicts 
WHERE file_number LIKE '%2025%'
ORDER BY file_number;
"

echo ""
echo "6. TESTING NULL/EMPTY FILE NUMBER HANDLING:"
echo "==========================================="
echo "Convicts without file numbers:"
sqlite3 "$DB_PATH" "
SELECT id, first_name, last_name, 
    CASE 
        WHEN file_number IS NULL THEN 'NULL'
        WHEN file_number = '' THEN 'EMPTY STRING'
        ELSE file_number 
    END as file_number_status
FROM convicts 
WHERE file_number IS NULL OR file_number = ''
ORDER BY id;
"

echo ""
echo "7. FINAL VERIFICATION:"
echo "====================="
echo "Total convicts: $(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM convicts;")"
echo "Convicts with file numbers: $(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM convicts WHERE file_number IS NOT NULL AND file_number != '';")"
echo "Convicts without file numbers: $(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM convicts WHERE file_number IS NULL OR file_number = '';")"

echo ""
echo "=== TESTING COMPLETE ==="
echo "The application should now be tested manually through the UI at http://localhost:1420"
echo "Use the test data created above to verify frontend functionality."
echo ""
echo "TEST CONVICTS CREATED:"
echo "- Test Convict1 (file_number: 2025/TEST1)"
echo "- Test Convict2 (file_number: 2025/UPDATED - was updated)"
echo "- Test Convict3 (file_number: 2025/156 İZM - Turkish chars)"
echo ""
echo "Next steps:"
echo "1. Open http://localhost:1420 in browser"
echo "2. Navigate to convict list and verify file numbers display"
echo "3. Test adding new convicts with/without file numbers"
echo "4. Test editing existing convicts' file numbers"
echo "5. Test Excel import/export functionality"
