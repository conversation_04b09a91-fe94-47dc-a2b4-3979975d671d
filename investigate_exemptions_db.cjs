const { execSync } = require('child_process');
const Database = require('better-sqlite3');

console.log('🔍 Database Exemption Investigation');
console.log('=====================================');

try {
  const db = new Database('./database.sqlite');
  
  // Check if exemptions table exists
  const tableInfo = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='exemptions'").get();
  if (!tableInfo) {
    console.log('❌ Exemptions table not found!');
    process.exit(1);
  }
  console.log('✅ Exemptions table exists');

  // Get all exemptions
  const allExemptions = db.prepare('SELECT * FROM exemptions').all();
  console.log(`📊 Total exemptions in database: ${allExemptions.length}`);
  
  if (allExemptions.length > 0) {
    console.log('📋 All exemptions:');
    allExemptions.forEach((exemption, index) => {
      console.log(`   ${index + 1}. ID: ${exemption.id}, Convict ID: ${exemption.convict_id}, Type: ${exemption.exemption_type}`);
      console.log(`      Dates: ${exemption.start_date} to ${exemption.end_date}`);
      console.log(`      Active: ${exemption.is_active}, Description: ${exemption.description || 'None'}`);
    });
  }

  // Get all convicts to check IDs
  const allConvicts = db.prepare('SELECT id, tc_no, first_name, last_name FROM convicts').all();
  console.log(`\n👥 Total convicts in database: ${allConvicts.length}`);
  
  if (allConvicts.length > 0) {
    console.log('📋 All convicts:');
    allConvicts.forEach((convict, index) => {
      console.log(`   ${index + 1}. ID: ${convict.id}, TC: ${convict.tc_no}, Name: ${convict.first_name} ${convict.last_name}`);
    });
  }

  // Find Miktat Güngör specifically
  const miktat = db.prepare("SELECT * FROM convicts WHERE first_name = 'Miktat' AND last_name = 'Güngör'").get();
  if (miktat) {
    console.log(`\n🎯 Found Miktat Güngör: ID ${miktat.id}, TC: ${miktat.tc_no}`);
    
    // Check exemptions for Miktat
    const miktatsExemptions = db.prepare('SELECT * FROM exemptions WHERE convict_id = ?').all(miktat.id);
    console.log(`   📋 Exemptions for Miktat: ${miktatsExemptions.length}`);
    
    if (miktatsExemptions.length > 0) {
      miktatsExemptions.forEach((exemption, index) => {
        console.log(`      ${index + 1}. Type: ${exemption.exemption_type}, Dates: ${exemption.start_date} to ${exemption.end_date}`);
        console.log(`         Active: ${exemption.is_active}, Description: ${exemption.description || 'None'}`);
      });
    } else {
      console.log('   ❌ No exemptions found for Miktat Güngör in database!');
      console.log('   🔍 This might explain why UI shows 0 exemptions');
    }
  } else {
    console.log('\n❌ Miktat Güngör not found in database!');
  }

  db.close();
  
} catch (error) {
  console.error('❌ Error investigating database:', error.message);
}

console.log('\n✅ Investigation complete');
