const Database = require('better-sqlite3');

console.log('🔍 TC Number Investigation & Fix');
console.log('===============================');

try {
  const db = new Database('./database.sqlite');
  
  console.log('📋 Current Situation:');
  console.log('1. Miktat Güngör (TC: 40261629268) - ID: 128 - NO exemptions');
  console.log('2. <PERSON><PERSON> (TC: 40267629040) - ID: 129 - HAS exemption');
  
  // Check current exemptions
  const miktatsExemptions = db.prepare('SELECT * FROM exemptions WHERE convict_id = 128').all();
  const yunusExemptions = db.prepare('SELECT * FROM exemptions WHERE convict_id = 129').all();
  
  console.log(`\n📊 Miktat Güngör (ID 128): ${miktatsExemptions.length} exemptions`);
  console.log(`📊 <PERSON><PERSON> (ID 129): ${yunusExemptions.length} exemptions`);
  
  if (yunusExemptions.length > 0) {
    console.log('\nYunus Güngör\'s exemptions:');
    yunusExemptions.forEach((exemption, index) => {
      console.log(`   ${index + 1}. ${exemption.exemption_type}: ${exemption.start_date} to ${exemption.end_date}`);
      console.log(`      Description: ${exemption.description || 'None'}`);
    });
  }
  
  console.log('\n🔧 SOLUTION OPTIONS:');
  console.log('Option 1: Move exemption from Yunus to Miktat (if it was meant for Miktat)');
  console.log('Option 2: Verify cache invalidation works for Yunus in UI');
  console.log('Option 3: Check Excel file TC numbers and re-import correctly');
  
  // Let's check if the exemption was meant for Miktat by looking at the description
  if (yunusExemptions.length > 0) {
    const exemption = yunusExemptions[0];
    console.log('\n🔍 ANALYSIS:');
    console.log(`Exemption description: "${exemption.description}"`);
    console.log('If this exemption was meant for Miktat Güngör, we should move it.');
    console.log('If not, the Excel file had the correct TC number and cache invalidation should work for Yunus.');
  }
  
  console.log('\n💡 RECOMMENDATION:');
  console.log('1. Test if exemption shows up for Yunus Güngör in UI');
  console.log('2. If yes, cache invalidation fix is working');
  console.log('3. If exemption was meant for Miktat, move it manually');
  
  db.close();
  
} catch (error) {
  console.error('❌ Error:', error.message);
}

console.log('\n✅ Investigation complete');
