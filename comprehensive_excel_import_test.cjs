const XLSX = require('xlsx');

// Comprehensive test of the Excel import process
async function testCompleteExcelImport() {
    console.log('=== Comprehensive Excel Import Test ===\n');
    
    try {
        // Read the Excel file
        const workbook = XLSX.readFile('hukumlu-listesi-2025-06-05.xlsx');
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        const headers = jsonData[0];
        const dataRows = jsonData.slice(1);
        
        console.log('1. File Analysis:');
        console.log('   Headers:', headers.length);
        console.log('   Data rows:', dataRows.length);
        
        // Replicate the exact parsing logic from the component
        const formatExcelDate = (value) => {
            if (!value) return '';
            
            // If it's already a date string, return it
            if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}$/)) {
                return value;
            }
            
            // If it's an Excel date number
            if (typeof value === 'number') {
                const date = XLSX.SSF.parse_date_code(value);
                if (date) {
                    return `${date.y.toString().padStart(4, '0')}-${date.m.toString().padStart(2, '0')}-${date.d.toString().padStart(2, '0')}`;
                }
            }
            
            // Try to parse as date
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
            
            return String(value);
        };
        
        const validateTcNo = (tcNo) => {
            if (!tcNo || tcNo.length !== 11) return false;
            const digits = tcNo.split('').map(Number);
            if (digits.some(isNaN)) return false;
            if (digits[0] === 0) return false;
            
            const sum1 = digits[0] + digits[2] + digits[4] + digits[6] + digits[8];
            const sum2 = digits[1] + digits[3] + digits[5] + digits[7];
            const check1 = (sum1 * 7 - sum2) % 10;
            if (check1 !== digits[9]) return false;
            
            const sum3 = digits.slice(0, 10).reduce((a, b) => a + b);
            const check2 = sum3 % 10;
            if (check2 !== digits[10]) return false;
            
            return true;
        };
        
        const validateSignaturePeriodData = (data, rowNumber) => {
            const errors = [];
            
            const startDate = data['İmza Periyodu Başlangıç'] ? formatExcelDate(data['İmza Periyodu Başlangıç']) : '';
            const endDate = data['İmza Periyodu Bitiş'] ? formatExcelDate(data['İmza Periyodu Bitiş']) : '';
            
            const frequencyTypeMapping = {
                'HAFTALIK': 'WEEKLY',
                'WEEKLY': 'WEEKLY',
                'GÜNLÜK': 'X_DAYS',
                'GUNLUK': 'X_DAYS',
                'X_DAYS': 'X_DAYS',
                'AYLIK': 'MONTHLY_SPECIFIC',
                'MONTHLY_SPECIFIC': 'MONTHLY_SPECIFIC',
                'AYLIK BELİRLİ GÜN': 'MONTHLY_SPECIFIC',
                'AYLIK BELIRLI GUN': 'MONTHLY_SPECIFIC'
            };
            
            const weekdayMapping = {
                'PAZARTESİ': 'MONDAY',
                'PAZARTESI': 'MONDAY',
                'SALI': 'TUESDAY',
                'ÇARŞAMBA': 'WEDNESDAY',
                'CARSAMBA': 'WEDNESDAY',
                'PERŞEMBE': 'THURSDAY',
                'PERSEMBE': 'THURSDAY',
                'CUMA': 'FRIDAY',
                'CUMARTESİ': 'SATURDAY',
                'CUMARTESI': 'SATURDAY',
                'PAZAR': 'SUNDAY'
            };
            
            const rawFrequencyType = String(data['İmza Sıklığı Türü'] || '').trim().toUpperCase();
            const frequencyType = frequencyTypeMapping[rawFrequencyType] || rawFrequencyType;
            
            let rawFrequencyValue = String(data['İmza Sıklığı Değeri'] || '').trim().toUpperCase();
            let processedFrequencyValue = rawFrequencyValue;
            
            if (frequencyType === 'WEEKLY') {
                if (rawFrequencyValue.includes(',')) {
                    const days = rawFrequencyValue.split(',').map(d => d.trim().toUpperCase());
                    const mappedDays = days.map(day => weekdayMapping[day] || day);
                    processedFrequencyValue = mappedDays.join(',');
                } else {
                    processedFrequencyValue = weekdayMapping[rawFrequencyValue] || rawFrequencyValue;
                }
            }
            
            const periodData = {
                convict_tc_no: String(data['TC Kimlik No'] || '').trim(),
                start_date: startDate,
                end_date: endDate,
                frequency_type: frequencyType,
                frequency_value: processedFrequencyValue,
                is_active: data['İmza Periyodu Aktif'] !== false && data['İmza Periyodu Aktif'] !== 'Hayır' && data['İmza Periyodu Aktif'] !== 'HAYIR',
            };
            
            // Check if signature period data exists
            const hasStartDate = data['İmza Periyodu Başlangıç'] !== undefined && data['İmza Periyodu Başlangıç'] !== '';
            const hasEndDate = data['İmza Periyodu Bitiş'] !== undefined && data['İmza Periyodu Bitiş'] !== '';
            const hasFrequencyType = data['İmza Sıklığı Türü'] !== undefined && data['İmza Sıklığı Türü'] !== '';
            const hasFrequencyValue = data['İmza Sıklığı Değeri'] !== undefined && data['İmza Sıklığı Değeri'] !== '';
            
            const hasSignaturePeriodData = hasStartDate || hasEndDate || hasFrequencyType || hasFrequencyValue;
            
            if (!hasSignaturePeriodData) {
                return null;
            }
            
            // Validate required fields
            if (!periodData.start_date) {
                errors.push({ field: 'start_date', message: 'İmza periyodu başlangıç tarihi gereklidir' });
            }
            
            if (!periodData.end_date) {
                errors.push({ field: 'end_date', message: 'İmza periyodu bitiş tarihi gereklidir' });
            }
            
            if (!periodData.frequency_type) {
                errors.push({ field: 'frequency_type', message: 'İmza sıklığı türü gereklidir' });
            }
            
            if (!periodData.frequency_value) {
                errors.push({ field: 'frequency_value', message: 'İmza sıklığı değeri gereklidir' });
            }
            
            // Validate frequency type and value
            const validFrequencyTypes = ['WEEKLY', 'X_DAYS', 'MONTHLY_SPECIFIC'];
            if (periodData.frequency_type && !validFrequencyTypes.includes(periodData.frequency_type)) {
                errors.push({ field: 'frequency_type', message: 'Geçersiz sıklık türü' });
            }
            
            // Validate frequency value based on type
            if (periodData.frequency_type === 'X_DAYS') {
                const dayValue = parseInt(periodData.frequency_value);
                if (isNaN(dayValue) || dayValue < 1 || dayValue > 30) {
                    errors.push({ field: 'frequency_value', message: 'X_DAYS için sıklık değeri 1-30 arası olmalıdır' });
                }
            } else if (periodData.frequency_type === 'MONTHLY_SPECIFIC') {
                // Updated validation to handle multiple days
                const dayValues = periodData.frequency_value.split(',').map(d => d.trim());
                let allDaysValid = true;
                
                if (dayValues.length === 0 || dayValues.some(d => d === '')) {
                    allDaysValid = false;
                } else {
                    for (const dayStr of dayValues) {
                        const dayValue = parseInt(dayStr);
                        if (isNaN(dayValue) || dayValue < 1 || dayValue > 31) {
                            allDaysValid = false;
                            break;
                        }
                    }
                }
                
                if (!allDaysValid) {
                    errors.push({ field: 'frequency_value', message: 'MONTHLY_SPECIFIC için sıklık değeri 1-31 arası gün numarası olmalıdır' });
                }
            } else if (periodData.frequency_type === 'WEEKLY') {
                const validWeekdays = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];
                if (!periodData.frequency_value) {
                    errors.push({ field: 'frequency_value', message: 'WEEKLY için sıklık değeri boş olamaz' });
                } else {
                    const days = periodData.frequency_value.split(',').map(d => d.trim().toUpperCase());
                    let allDaysValid = true;
                    if (days.length === 0 || days.some(d => d === '')) {
                        allDaysValid = false;
                    }
                    for (const day of days) {
                        if (day && !validWeekdays.includes(day)) {
                            allDaysValid = false;
                            break;
                        }
                    }
                    if (!allDaysValid) {
                        errors.push({ field: 'frequency_value', message: 'WEEKLY için geçersiz gün adları' });
                    }
                }
            }
            
            // Date validation
            if (periodData.start_date && periodData.end_date) {
                const startDate = new Date(periodData.start_date);
                const endDate = new Date(periodData.end_date);
                
                if (endDate <= startDate) {
                    errors.push({ field: 'end_date', message: 'Bitiş tarihi başlangıç tarihinden sonra olmalıdır' });
                }
            }
            
            return {
                ...periodData,
                rowNumber,
                isValid: errors.length === 0,
                errors,
            };
        };
        
        console.log('\n2. Processing and Validation:');
        
        const parsedConvicts = [];
        const parsedSignaturePeriods = [];
        const parsedExemptions = [];
        
        // Group by TC number
        const rowsByTcNo = new Map();
        
        dataRows.forEach((row, index) => {
            if (row.some(cell => cell !== undefined && cell !== '')) {
                const rowData = {};
                headers.forEach((header, headerIndex) => {
                    rowData[header] = row[headerIndex];
                });
                
                const tcNo = String(rowData['TC Kimlik No'] || '').trim();
                if (tcNo) {
                    const existingRows = rowsByTcNo.get(tcNo) || [];
                    rowsByTcNo.set(tcNo, [...existingRows, { rowNumber: index + 2, data: rowData }]);
                }
            }
        });
        
        console.log('   Unique TC numbers:', rowsByTcNo.size);
        
        for (const [tcNo, rows] of rowsByTcNo) {
            console.log(`\n   Processing TC: ${tcNo} (${rows.length} rows)`);
            
            // Validate convict data from first row
            const firstRow = rows[0];
            const convictData = {
                tc_no: String(firstRow.data['TC Kimlik No'] || '').trim(),
                first_name: String(firstRow.data['Ad'] || '').trim(),
                last_name: String(firstRow.data['Soyad'] || '').trim(),
                phone_number: String(firstRow.data['Hükümlü Telefon Numarası'] || '').trim() || undefined,
                relative_phone_number: String(firstRow.data['Yakınına Ait Telefon Numarası'] || '').trim() || undefined,
                address: String(firstRow.data['Adres Bilgileri'] || '').trim() || undefined,
                file_number: String(firstRow.data['Dosya Numarası'] || '').trim() || undefined,
                supervision_start_date: firstRow.data['Denetim Başlangıç Tarihi'] ? 
                    formatExcelDate(firstRow.data['Denetim Başlangıç Tarihi']) : '',
                supervision_end_date: firstRow.data['Denetim Bitiş Tarihi'] ? 
                    formatExcelDate(firstRow.data['Denetim Bitiş Tarihi']) : '',
                is_active: firstRow.data['Aktif'] !== false && firstRow.data['Aktif'] !== 'Hayır' && firstRow.data['Aktif'] !== 'HAYIR',
                notes: String(firstRow.data['Notlar'] || '').trim() || undefined,
            };
            
            const convictErrors = [];
            
            // Validate TC number
            if (!convictData.tc_no) {
                convictErrors.push('TC Kimlik No gereklidir');
            } else if (!validateTcNo(convictData.tc_no)) {
                convictErrors.push('Geçersiz TC Kimlik No');
            }
            
            // Validate names
            if (!convictData.first_name) {
                convictErrors.push('Ad gereklidir');
            }
            if (!convictData.last_name) {
                convictErrors.push('Soyad gereklidir');
            }
            
            // Validate dates
            if (!convictData.supervision_start_date) {
                convictErrors.push('Denetim başlangıç tarihi gereklidir');
            }
            if (!convictData.supervision_end_date) {
                convictErrors.push('Denetim bitiş tarihi gereklidir');
            }
            
            if (convictData.supervision_start_date && convictData.supervision_end_date) {
                const startDate = new Date(convictData.supervision_start_date);
                const endDate = new Date(convictData.supervision_end_date);
                
                if (endDate <= startDate) {
                    convictErrors.push('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
                }
            }
            
            const convictIsValid = convictErrors.length === 0;
            console.log(`     Convict valid: ${convictIsValid}${convictErrors.length > 0 ? ` (errors: ${convictErrors.join(', ')})` : ''}`);
            
            if (convictIsValid) {
                parsedConvicts.push({
                    ...convictData,
                    rowNumber: firstRow.rowNumber,
                    isValid: true,
                    errors: []
                });
            }
            
            // Process signature periods and exemptions for each row
            rows.forEach(({ data, rowNumber }) => {
                // Signature period
                const validatedSignaturePeriod = validateSignaturePeriodData(data, rowNumber);
                if (validatedSignaturePeriod) {
                    console.log(`     Row ${rowNumber} signature period: ${validatedSignaturePeriod.isValid ? 'VALID' : 'INVALID'}`);
                    if (validatedSignaturePeriod.isValid) {
                        // Handle multiple days for WEEKLY and MONTHLY_SPECIFIC
                        if (validatedSignaturePeriod.frequency_type === 'WEEKLY' && validatedSignaturePeriod.frequency_value.includes(',')) {
                            const individualDays = validatedSignaturePeriod.frequency_value.split(',').map(d => d.trim().toUpperCase());
                            individualDays.forEach(day => {
                                if (day) {
                                    parsedSignaturePeriods.push({
                                        ...validatedSignaturePeriod,
                                        frequency_value: day,
                                    });
                                }
                            });
                            console.log(`       Created ${individualDays.length} WEEKLY periods for days: ${individualDays.join(', ')}`);
                        } else if (validatedSignaturePeriod.frequency_type === 'MONTHLY_SPECIFIC' && validatedSignaturePeriod.frequency_value.includes(',')) {
                            const individualDays = validatedSignaturePeriod.frequency_value.split(',').map(d => d.trim());
                            individualDays.forEach(day => {
                                if (day) {
                                    parsedSignaturePeriods.push({
                                        ...validatedSignaturePeriod,
                                        frequency_value: day,
                                    });
                                }
                            });
                            console.log(`       Created ${individualDays.length} MONTHLY_SPECIFIC periods for days: ${individualDays.join(', ')}`);
                        } else {
                            parsedSignaturePeriods.push(validatedSignaturePeriod);
                            console.log(`       Created 1 ${validatedSignaturePeriod.frequency_type} period`);
                        }
                    } else {
                        console.log(`       Errors: ${validatedSignaturePeriod.errors.map(e => e.message).join(', ')}`);
                    }
                }
                
                // Exemption (simplified validation)
                const exemptionType = String(data['Muafiyet Türü'] || '').trim();
                const exemptionStart = data['Muafiyet Başlangıç'] ? formatExcelDate(data['Muafiyet Başlangıç']) : '';
                const exemptionEnd = data['Muafiyet Bitiş'] ? formatExcelDate(data['Muafiyet Bitiş']) : '';
                
                if (exemptionType && exemptionStart && exemptionEnd) {
                    const exemptionTypeMapping = {
                        'İZİN': 'LEAVE',
                        'IZIN': 'LEAVE',
                        'LEAVE': 'LEAVE',
                        'SAĞLIK RAPORU': 'MEDICAL_REPORT',
                        'SAGLIK RAPORU': 'MEDICAL_REPORT',
                        'MEDICAL_REPORT': 'MEDICAL_REPORT',
                        'SAĞLIK': 'MEDICAL_REPORT',
                        'SAGLIK': 'MEDICAL_REPORT'
                    };
                    
                    const mappedType = exemptionTypeMapping[exemptionType.toUpperCase()] || exemptionType;
                    
                    parsedExemptions.push({
                        convict_tc_no: tcNo,
                        exemption_type: mappedType,
                        start_date: exemptionStart,
                        end_date: exemptionEnd,
                        description: String(data['Muafiyet Açıklaması'] || '').trim() || undefined,
                        document_path: String(data['Muafiyet Belgesi'] || '').trim() || undefined,
                        is_active: data['Muafiyet Aktif'] !== false && data['Muafiyet Aktif'] !== 'Hayır' && data['Muafiyet Aktif'] !== 'HAYIR',
                        rowNumber,
                        isValid: true,
                        errors: []
                    });
                    console.log(`     Row ${rowNumber} exemption: VALID (${mappedType})`);
                }
            });
        }
        
        console.log('\n3. Final Results:');
        console.log(`   Valid convicts: ${parsedConvicts.length}`);
        console.log(`   Valid signature periods: ${parsedSignaturePeriods.length}`);
        console.log(`   Valid exemptions: ${parsedExemptions.length}`);
        
        console.log('\n4. Signature Period Breakdown:');
        const weeklyCount = parsedSignaturePeriods.filter(p => p.frequency_type === 'WEEKLY').length;
        const monthlyCount = parsedSignaturePeriods.filter(p => p.frequency_type === 'MONTHLY_SPECIFIC').length;
        const xDaysCount = parsedSignaturePeriods.filter(p => p.frequency_type === 'X_DAYS').length;
        
        console.log(`   WEEKLY: ${weeklyCount}`);
        console.log(`   MONTHLY_SPECIFIC: ${monthlyCount}`);
        console.log(`   X_DAYS: ${xDaysCount}`);
        
        console.log('\n5. Data Structure Validation:');
        
        // Validate that all signature periods have required fields
        const invalidPeriods = parsedSignaturePeriods.filter(p => 
            !p.convict_tc_no || !p.start_date || !p.end_date || !p.frequency_type || !p.frequency_value
        );
        
        if (invalidPeriods.length > 0) {
            console.log(`   ❌ Found ${invalidPeriods.length} invalid signature periods`);
            invalidPeriods.forEach(p => {
                console.log(`      TC: ${p.convict_tc_no}, Missing: ${Object.entries(p).filter(([k, v]) => !v && ['convict_tc_no', 'start_date', 'end_date', 'frequency_type', 'frequency_value'].includes(k)).map(([k]) => k).join(', ')}`);
            });
        } else {
            console.log('   ✅ All signature periods have required fields');
        }
        
        // Validate that all exemptions have required fields
        const invalidExemptions = parsedExemptions.filter(e => 
            !e.convict_tc_no || !e.exemption_type || !e.start_date || !e.end_date
        );
        
        if (invalidExemptions.length > 0) {
            console.log(`   ❌ Found ${invalidExemptions.length} invalid exemptions`);
        } else {
            console.log('   ✅ All exemptions have required fields');
        }
        
        console.log('\n=== Test Complete ===');
        console.log('Excel import parsing is working correctly!');
        console.log('Ready for import into database.');
        
    } catch (error) {
        console.error('Error in comprehensive test:', error);
    }
}

testCompleteExcelImport();
