import { useParams } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import DataTable from '@/components/common/DataTable';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import SignaturePeriodForm from './SignaturePeriodForm';
import { useToast } from '@/hooks/use-toast';
import { getConvicts, getConvictSignaturePeriods, createSignaturePeriod, updateSignaturePeriod, deleteSignaturePeriod } from '@/lib/tauri-api';
import { formatDate, getFrequencyText } from '@/lib/utils';
import { groupSignaturePeriods } from '@/lib/signature-dates';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import type { ConvictWithDetails, SignaturePeriod, InsertSignaturePeriod } from '@shared/schema';

// SignaturePeriodForm'daki weekDays tanımına benzer bir eşleme.
// İdealde bu, paylaşılan bir yerden import edilebilir.
const weekDayBackendToFormValue: { [key: string]: number } = {
  MONDAY: 0,
  TUESDAY: 1,
  WEDNESDAY: 2,
  THURSDAY: 3,
  FRIDAY: 4,
  SATURDAY: 5,
  SUNDAY: 6,
};

export default function ManageSignaturePeriodsPage() {
  const { id } = useParams();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPeriod, setEditingPeriod] = useState<SignaturePeriod | null>(null);

  const { data: convicts, isLoading: convictLoading } = useQuery<ConvictWithDetails[]>({
    queryKey: ['convicts'],
    queryFn: async () => {
      const convicts = await getConvicts();
      return convicts.map(convict => ({
        ...convict,
        fullName: `${convict.first_name} ${convict.last_name}`,
        activePeriods: [],
        lastSignature: undefined
      }));
    },
  });

  const convict = convicts?.find(c => c.id === Number(id));

  const { data: periods, isLoading: periodsLoading } = useQuery<SignaturePeriod[]>({
    queryKey: ['signature-periods', id],
    queryFn: () => getConvictSignaturePeriods(Number(id)),
    enabled: !!id,
  });

  // Group periods for display
  const groupedPeriods = periods ? groupSignaturePeriods(periods) : [];

  const createPeriodMutation = useMutation({
    mutationFn: async (data: Omit<InsertSignaturePeriod, 'convict_id'>) => {
      const periodData: InsertSignaturePeriod = {
        ...data,
        convict_id: Number(id),
      };
      return await createSignaturePeriod(periodData);
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'İmza periyodu eklendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['signature-periods', id] });
      setIsDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'İmza periyodu eklenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const updatePeriodMutation = useMutation({
    mutationFn: async (data: { id: number; period: Omit<InsertSignaturePeriod, 'convict_id'> }) => {
      const periodData: InsertSignaturePeriod = {
        ...data.period,
        convict_id: Number(id),
      };
      return await updateSignaturePeriod(data.id, periodData);
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'İmza periyodu güncellendi.',
      });
      queryClient.invalidateQueries({ queryKey: ['signature-periods', id] });
      setIsDialogOpen(false);
      setEditingPeriod(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'İmza periyodu güncellenirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const deletePeriodMutation = useMutation({
    mutationFn: async (periodId: number) => {
      return await deleteSignaturePeriod(periodId);
    },
    onSuccess: () => {
      toast({
        title: 'Başarılı',
        description: 'İmza periyodu silindi.',
      });
      queryClient.invalidateQueries({ queryKey: ['signature-periods', id] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Hata',
        description: error.message || 'İmza periyodu silinirken bir hata oluştu.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (data: Omit<InsertSignaturePeriod, 'convict_id'>) => {
    if (editingPeriod) {
      updatePeriodMutation.mutate({ id: editingPeriod.id, period: data });
    } else {
      createPeriodMutation.mutate(data);
    }
  };

  const handleEdit = (period: SignaturePeriod) => {
    setEditingPeriod(period);
    setIsDialogOpen(true);
  };

  const handleDelete = (period: SignaturePeriod) => {
    // Find all original periods that match this grouped period
    const matchingOriginalPeriods = periods?.filter(p => 
      p.start_date === period.start_date &&
      p.end_date === period.end_date &&
      p.frequency_type === period.frequency_type &&
      p.time_start === period.time_start &&
      p.time_end === period.time_end
    ) || [];

    const isGrouped = matchingOriginalPeriods.length > 1;
    const confirmMessage = isGrouped 
      ? `Bu gruplanan ${matchingOriginalPeriods.length} imza periyodunu silmek istediğinizden emin misiniz?\n\nSilinecek periyotlar:\n${matchingOriginalPeriods.map(p => `- ${getFrequencyText(p.frequency_type, p.frequency_value)}`).join('\n')}`
      : 'Bu imza periyodunu silmek istediğinizden emin misiniz?';

    if (confirm(confirmMessage)) {
      // Delete all matching periods
      matchingOriginalPeriods.forEach(originalPeriod => {
        deletePeriodMutation.mutate(originalPeriod.id);
      });
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingPeriod(null);
  };

  if (convictLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 font-medium">Hükümlü bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (!convict) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">!</span>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Hükümlü Bulunamadı</h2>
          <p className="text-gray-600">Aradığınız hükümlü kaydı bulunamadı.</p>
        </div>
      </div>
    );
  }

  const columns = [
    {
      key: 'startDate',
      label: 'Başlangıç Tarihi',
      render: (period: SignaturePeriod) => (
        <div className="font-medium text-gray-900">
          {formatDate(period.start_date)}
        </div>
      )
    },
    {
      key: 'endDate',
      label: 'Bitiş Tarihi',
      render: (period: SignaturePeriod) => (
        <div className="font-medium text-gray-900">
          {formatDate(period.end_date)}
        </div>
      )
    },
    {
      key: 'frequency',
      label: 'Sıklık',
      render: (period: SignaturePeriod) => (
        <div className="text-sm">
          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
            {getFrequencyText(period.frequency_type, period.frequency_value)}
          </span>
        </div>
      )
    },
    {
      key: 'timeRestrictions',
      label: 'Saat Kısıtlamaları',
      render: (period: SignaturePeriod) => (
        <div className="text-sm text-gray-600">
          {period.time_start && period.time_end 
            ? `${period.time_start} - ${period.time_end}` 
            : 'Kısıt yok'
          }
        </div>
      )
    },
    {
      key: 'isActive',
      label: 'Durum',
      render: (period: SignaturePeriod) => (
        <Badge 
          variant={period.is_active ? 'default' : 'secondary'}
          className={`font-medium ${
            period.is_active 
              ? 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200' 
              : 'bg-gray-100 text-gray-600 border-gray-200'
          }`}
        >
          {period.is_active ? '✅ Aktif' : '⏸️ Pasif'}
        </Badge>
      )
    }
  ];

  const actions = (period: SignaturePeriod) => {
    // Find original periods that match this grouped period
    const matchingOriginalPeriods = periods?.filter(p => 
      p.start_date === period.start_date &&
      p.end_date === period.end_date &&
      p.frequency_type === period.frequency_type &&
      p.time_start === period.time_start &&
      p.time_end === period.time_end
    ) || [];

    const isGrouped = matchingOriginalPeriods.length > 1;
    
    // For display count, use the number of days/values in the grouped period, not the number of database records
    let displayCount = matchingOriginalPeriods.length;
    if (period.frequency_type === 'WEEKLY' || period.frequency_type === 'MONTHLY_SPECIFIC') {
      // Count unique days/values in the frequency_value
      const uniqueDays = period.frequency_value ? 
        [...new Set(period.frequency_value.split(',').map(d => d.trim()).filter(d => d))] : 
        [];
      displayCount = uniqueDays.length;
    }

    return (
      <div className="flex items-center justify-end space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleEdit(period)}
          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200"
          title={isGrouped ? "Gruplanmış periyottan ilk period düzenlenecek" : "Periyodu düzenle"}
        >
          <PencilIcon className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleDelete(period)}
          className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
          title={isGrouped ? `${matchingOriginalPeriods.length} gruplanmış periyot silinecek` : "Periyodu sil"}
        >
          <TrashIcon className="w-4 h-4" />
          {isGrouped && <span className="ml-1 text-xs">({displayCount})</span>}
        </Button>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 text-white p-8 rounded-xl mx-6 mt-6 mb-6 shadow-lg backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {convict.fullName} İçin İmza Periyotları
            </h1>
            <p className="text-blue-100 text-lg">
              Hükümlünün imza periyotlarını yönetin ve düzenleyin
            </p>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-blue-200">TC Kimlik No</p>
              <p className="text-xl font-semibold">{convict.tc_no}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 pb-6">
        <Card className="backdrop-blur-sm bg-white/70 border-white/20 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-xl flex flex-row items-center justify-between">
            <CardTitle className="text-gray-800 flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              İmza Periyotları
            </CardTitle>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  onClick={() => setEditingPeriod(null)}
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  size="sm"
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Yeni Periyot
                </Button>
              </DialogTrigger>
              <DialogContent className="backdrop-blur-sm bg-white/95 border-white/20 shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="text-xl font-semibold text-gray-800">
                    {editingPeriod ? 'İmza Periyodu Düzenle' : 'Yeni İmza Periyodu'}
                  </DialogTitle>
                </DialogHeader>
                <SignaturePeriodForm
                  convict={convict}
                  initialData={editingPeriod ? {
                    startDate: editingPeriod.start_date,
                    endDate: editingPeriod.end_date,
                    frequencyType: editingPeriod.frequency_type,
                    frequencyValue: 
                      editingPeriod.frequency_type === 'WEEKLY' 
                      ? (editingPeriod.frequency_value || '').split(',')
                          .map(dayName => weekDayBackendToFormValue[dayName.trim()])
                          .filter(value => value !== undefined) // Eşleşmeyen veya boş değerleri kaldır
                      : editingPeriod.frequency_type === 'X_DAYS'
                      ? parseInt(editingPeriod.frequency_value) || '' // Sayıya çevir, string ise boş string
                      : editingPeriod.frequency_value, // MONTHLY_SPECIFIC veya diğerleri için olduğu gibi bırak
                    referenceDate: editingPeriod.reference_date || undefined,
                    isActive: editingPeriod.is_active,
                  } : undefined}
                  onSubmit={handleSubmit}
                  onCancel={handleDialogClose}
                  isLoading={createPeriodMutation.isPending || updatePeriodMutation.isPending}
                  submitLabel={editingPeriod ? 'Güncelle' : 'Ekle'}
                />
              </DialogContent>
            </Dialog>
          </CardHeader>
          <CardContent className="p-6">
            <DataTable
              data={groupedPeriods}
              columns={columns}
              actions={actions}
              loading={periodsLoading}
              emptyMessage="Henüz imza periyodu eklenmemiş"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
