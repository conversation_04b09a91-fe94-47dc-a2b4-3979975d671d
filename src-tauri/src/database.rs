use anyhow::Result;
use bcrypt;
use rusqlite::{params, Connection, Row, OptionalExtension};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use chrono::{NaiveDate, Datelike, Weekday, Utc};

// Database types matching the Drizzle schema
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct User {
    pub id: i32,
    pub username: String,
    pub password: String,
    pub role: String,
    pub is_active: bool,
    pub created_at: Option<String>, // Changed to String to match Drizzle
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Convict {
    pub id: i32,
    pub tc_no: String,
    pub first_name: String,
    pub last_name: String,
    pub supervision_start_date: String,
    pub supervision_end_date: String,
    pub phone_number: Option<String>,
    pub relative_phone_number: Option<String>,
    pub address: Option<String>,
    pub file_number: Option<String>,
    pub is_active: bool,
    pub notes: Option<String>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct SignaturePeriod {
    pub id: i32,
    pub convict_id: i32,
    pub start_date: String,
    pub end_date: String,
    pub frequency_type: String,
    pub frequency_value: String,
    pub reference_date: Option<String>,
    pub time_start: Option<String>, // Start time like "10:00"
    pub time_end: Option<String>, // End time like "22:00"
    pub allowed_days: Option<String>, // JSON array of allowed weekdays
    pub is_active: bool,
    pub created_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Signature {
    pub id: i32,
    pub convict_id: i32,
    pub signature_date: String,
    pub signature_time: String,
    pub recorded_by: Option<i32>,
    pub created_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Exemption {
    pub id: i32,
    pub convict_id: i32,
    pub exemption_type: String,
    pub start_date: String,
    pub end_date: String,
    pub description: Option<String>,
    pub document_path: Option<String>,
    pub created_by: Option<i32>,
    pub is_active: bool,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InsertUser {
    pub username: String,
    pub password: String,
    pub role: String,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct InsertConvict {
    pub tc_no: String,
    pub first_name: String,
    pub last_name: String,
    pub supervision_start_date: String,
    pub supervision_end_date: String,
    pub phone_number: Option<String>,
    pub relative_phone_number: Option<String>,
    pub address: Option<String>,
    pub file_number: Option<String>,
    pub is_active: Option<bool>,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InsertSignaturePeriod {
    pub convict_id: i32,
    pub start_date: String,
    pub end_date: String,
    pub frequency_type: String,
    pub frequency_value: String,
    pub reference_date: Option<String>,
    pub time_start: Option<String>,
    pub time_end: Option<String>,
    pub allowed_days: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InsertSignature {
    pub convict_id: i32,
    pub signature_date: String,
    pub signature_time: String,
    pub recorded_by: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InsertExemption {
    pub convict_id: i32,
    pub exemption_type: String,
    pub start_date: String,
    pub end_date: String,
    pub description: Option<String>,
    pub document_path: Option<String>,
    pub created_by: Option<i32>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardStats {
    pub total_convicts: i32,
    pub active_convicts: i32,
    pub expected_today: i32,
    pub completed_today: i32,
    pub violations: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CompletedSignature {
    pub convict_id: i32,
    pub tc_no: String,
    pub first_name: String,
    pub last_name: String,
    pub signature_time: String,
    pub signature_date: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExpectedSignature {
    pub convict_id: i32,
    pub tc_no: String,
    pub first_name: String,
    pub last_name: String,
    pub has_signed: bool,
    pub signature_time: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ViolationRecord {
    pub convict_id: i32,
    pub tc_no: String,
    pub first_name: String,
    pub last_name: String,
    pub violation_date: String,
    pub expected_frequency: String,
}

// Full convict details for bulk export
#[derive(Debug, Serialize, Deserialize)]
pub struct ConvictFullDetails {
    pub id: i32,
    pub tc_no: String,
    pub first_name: String,
    pub last_name: String,
    pub phone_number: Option<String>,
    pub relative_phone_number: Option<String>,
    pub address: Option<String>,
    pub file_number: Option<String>,
    pub supervision_start_date: String,
    pub supervision_end_date: String,
    pub is_active: bool,
    pub notes: Option<String>,
    pub signature_periods: Option<Vec<SignaturePeriod>>,
    pub exemptions: Option<Vec<Exemption>>,
}

pub type DatabaseConnection = Arc<Mutex<Connection>>;

// Get the database path - same as Drizzle uses
pub fn get_database_path() -> PathBuf {
    // When running from Tauri, we need to go up one level from src-tauri to the project root
    let mut path = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
    
    // If we're in src-tauri directory, go up one level
    if path.file_name().and_then(|s| s.to_str()) == Some("src-tauri") {
        path.pop();
    }
    
    path.push("database.sqlite");
    path
}

pub fn init_database() -> Result<DatabaseConnection> {
    let db_path = get_database_path();
    let conn = Connection::open(&db_path)?;
    
    // Enable foreign keys to match Drizzle
    conn.execute("PRAGMA foreign_keys = ON", [])?;
    
    // Note: We don't create tables here anymore since Drizzle handles migrations
    // This function now just opens the existing database
    
    let db_connection = Arc::new(Mutex::new(conn));
    
    // Validate and fix any data type issues
    match validate_and_fix_signatures_data(&db_connection) {
        Ok(fixed_count) => {
            if fixed_count > 0 {
                println!("Fixed {} signatures with data type issues", fixed_count);
            }
        }
        Err(e) => {
            println!("Warning: Could not validate signatures data: {}", e);
        }
    }
    
    println!("Connected to SQLite database at: {:?}", db_path);
    
    Ok(db_connection)
}

// Row mapping functions - compatible with Drizzle schema
fn row_to_user(row: &Row) -> rusqlite::Result<User> {
    Ok(User {
        id: row.get(0)?,
        username: row.get(1)?,
        password: row.get(2)?,
        role: row.get(3)?,
        is_active: row.get::<_, i32>(4)? != 0, // SQLite stores boolean as integer
        created_at: row.get(5)?,
    })
}

fn row_to_convict(row: &Row) -> rusqlite::Result<Convict> {
    Ok(Convict {
        id: row.get(0)?,
        tc_no: row.get(1)?,
        first_name: row.get(2)?,
        last_name: row.get(3)?,
        supervision_start_date: row.get(4)?,
        supervision_end_date: row.get(5)?,
        phone_number: row.get(6)?,
        relative_phone_number: row.get(7)?,
        address: row.get(8)?,
        file_number: row.get(9)?,
        is_active: row.get::<_, i32>(10)? != 0, // SQLite stores boolean as integer
        notes: row.get(11)?,
        created_at: row.get(12)?,
        updated_at: row.get(13)?,
    })
}

fn row_to_signature(row: &Row) -> rusqlite::Result<Signature> {
    Ok(Signature {
        id: row.get(0)?,
        convict_id: row.get(1)?,
        signature_date: row.get(2)?,
        signature_time: row.get(3)?,
        recorded_by: row.get::<_, Option<i32>>(4)?,
        created_at: row.get::<_, Option<String>>(5)?,
    })
}

fn row_to_signature_period(row: &Row) -> rusqlite::Result<SignaturePeriod> {
    Ok(SignaturePeriod {
        id: row.get(0)?,
        convict_id: row.get(1)?,
        start_date: row.get(2)?,
        end_date: row.get(3)?,
        frequency_type: row.get(4)?,
        frequency_value: row.get(5)?,
        reference_date: row.get(6)?,
        is_active: row.get::<_, i32>(7)? != 0, // SQLite stores boolean as integer
        created_at: row.get(8)?,
        time_start: row.get(9)?,
        time_end: row.get(10)?,
        allowed_days: row.get(11)?,
    })
}

fn row_to_exemption(row: &Row) -> rusqlite::Result<Exemption> {
    Ok(Exemption {
        id: row.get(0)?,
        convict_id: row.get(1)?,
        exemption_type: row.get(2)?,
        start_date: row.get(3)?,
        end_date: row.get(4)?,
        description: row.get(5)?,
        document_path: row.get(6)?,
        created_by: row.get::<_, Option<i32>>(7)?,
        is_active: row.get::<_, i32>(8)? != 0, // SQLite stores boolean as integer
        created_at: row.get(9)?,
        updated_at: row.get(10)?,
    })
}

// Additional utility functions for Drizzle integration

pub fn get_all_users(db: &DatabaseConnection) -> Result<Vec<User>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, username, password, role, is_active, created_at FROM users ORDER BY created_at DESC"
    )?;
    
    let user_iter = stmt.query_map([], |row| row_to_user(row))?;
    
    let mut users = Vec::new();
    for user in user_iter {
        users.push(user?);
    }
    
    Ok(users)
}

pub fn find_user_by_username(db: &DatabaseConnection, username: &str) -> Result<Option<User>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, username, password, role, is_active, created_at FROM users WHERE username = ?1"
    )?;
    
    let mut user_iter = stmt.query_map([username], |row| row_to_user(row))?;
    
    match user_iter.next() {
        Some(user) => Ok(Some(user?)),
        None => Ok(None),
    }
}

pub fn get_signature_periods_by_convict(db: &DatabaseConnection, convict_id: i32) -> Result<Vec<SignaturePeriod>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days 
         FROM signature_periods 
         WHERE convict_id = ?1 
         ORDER BY created_at DESC"
    )?;
    
    let period_iter = stmt.query_map([convict_id], |row| row_to_signature_period(row))?;
    
    let mut periods = Vec::new();
    for period in period_iter {
        periods.push(period?);
    }
    
    Ok(periods)
}

pub fn create_signature_period(db: &DatabaseConnection, period: InsertSignaturePeriod) -> Result<SignaturePeriod> {
    let conn = db.lock().unwrap();
    
    conn.execute(
        "INSERT INTO signature_periods (convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, time_start, time_end, allowed_days) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
        params![
            period.convict_id,
            period.start_date,
            period.end_date,
            period.frequency_type,
            period.frequency_value,
            period.reference_date,
            period.is_active.unwrap_or(true),
            period.time_start,
            period.time_end,
            period.allowed_days
        ],
    )?;

    let period_id = conn.last_insert_rowid() as i32;
    
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days 
         FROM signature_periods WHERE id = ?1"
    )?;
    let period = stmt.query_row([period_id], |row| row_to_signature_period(row))?;
    
    Ok(period)
}

// User operations
pub fn create_user(db: &DatabaseConnection, user: InsertUser) -> Result<User> {
    let conn = db.lock().unwrap();
    
    // Hash password using bcrypt
    let hashed_password = bcrypt::hash(&user.password, bcrypt::DEFAULT_COST)?;
    
    let is_active_val = user.is_active.unwrap_or(true);

    conn.execute(
        "INSERT INTO users (username, password, role, is_active) VALUES (?1, ?2, ?3, ?4)",
        params![user.username, hashed_password, user.role, is_active_val],
    )?;
    
    let id = conn.last_insert_rowid() as i32;
    
    // Retrieve the created user to return it
    conn.query_row(
        "SELECT id, username, password, role, is_active, created_at FROM users WHERE id = ?1",
        params![id],
        row_to_user,
    )
    .map_err(|e| e.into())
}

pub fn update_user(db: &DatabaseConnection, id: i32, user_data: InsertUser) -> Result<User> {
    let conn = db.lock().unwrap();

    // Check if password needs to be updated
    let mut set_clauses = Vec::new();
    let mut query_params: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();
    
    // Username
    set_clauses.push(format!("username = ?{}", query_params.len() + 1));
    query_params.push(Box::new(user_data.username));

    // Role
    set_clauses.push(format!("role = ?{}", query_params.len() + 1));
    query_params.push(Box::new(user_data.role));
    
    // is_active
    if let Some(is_active) = user_data.is_active {
        set_clauses.push(format!("is_active = ?{}", query_params.len() + 1));
        query_params.push(Box::new(is_active));
    }

    // Password (only if provided and not empty)
    if !user_data.password.is_empty() {
        let hashed_password = bcrypt::hash(&user_data.password, bcrypt::DEFAULT_COST)?;
        set_clauses.push(format!("password = ?{}", query_params.len() + 1));
        query_params.push(Box::new(hashed_password));
    }
    
    query_params.push(Box::new(id)); // For the WHERE clause

    if set_clauses.is_empty() {
        // No fields to update, just return the existing user
        return conn.query_row(
            "SELECT id, username, password, role, is_active, created_at FROM users WHERE id = ?1",
            params![id],
            row_to_user,
        ).map_err(|e| e.into());
    }

    let sql = format!(
        "UPDATE users SET {} WHERE id = ?{}",
        set_clauses.join(", "),
        query_params.len() // This will be the ID parameter
    );

    conn.execute(&sql, rusqlite::params_from_iter(query_params))?;

    // Retrieve the updated user to return it
    conn.query_row(
        "SELECT id, username, password, role, is_active, created_at FROM users WHERE id = ?1",
        params![id],
        row_to_user,
    )
    .map_err(|e| e.into())
}

pub fn delete_user(db: &DatabaseConnection, id: i32) -> Result<bool> {
    let conn = db.lock().unwrap();
    let rows_affected = conn.execute("DELETE FROM users WHERE id = ?1", params![id])?;
    Ok(rows_affected > 0)
}

// Convict operations
pub fn get_convicts(db: &DatabaseConnection) -> Result<Vec<Convict>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts ORDER BY created_at DESC"
    )?;
    
    let convict_iter = stmt.query_map([], |row| row_to_convict(row))?;
    
    let mut convicts = Vec::new();
    for convict in convict_iter {
        convicts.push(convict?);
    }
    
    Ok(convicts)
}

pub fn create_convict(db: &DatabaseConnection, convict: InsertConvict) -> Result<Convict> {
    let conn = db.lock().unwrap();
    
    conn.execute(
        "INSERT INTO convicts (tc_no, first_name, last_name, supervision_start_date, supervision_end_date, phone_number, relative_phone_number, address, file_number, is_active, notes) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)",
        params![
            convict.tc_no,
            convict.first_name,
            convict.last_name,
            convict.supervision_start_date,
            convict.supervision_end_date,
            convict.phone_number,
            convict.relative_phone_number,
            convict.address,
            convict.file_number,
            convict.is_active.unwrap_or(true),
            convict.notes
        ],
    )?;

    let convict_id = conn.last_insert_rowid() as i32;
    
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts WHERE id = ?1"
    )?;
    let convict = stmt.query_row([convict_id], |row| row_to_convict(row))?;
    
    Ok(convict)
}

// Upsert function: Create or update convict based on TC number
pub fn upsert_convict(db: &DatabaseConnection, convict: InsertConvict) -> Result<(Convict, bool)> {
    let conn = db.lock().unwrap();
    
    // First, check if a convict with this TC number already exists
    let mut check_stmt = conn.prepare("SELECT id FROM convicts WHERE tc_no = ?1")?;
    let existing_id: Option<i32> = check_stmt.query_row([&convict.tc_no], |row| row.get(0)).optional()?;
    
    if let Some(id) = existing_id {
        // Update existing convict
        conn.execute(
            "UPDATE convicts SET first_name = ?1, last_name = ?2, supervision_start_date = ?3, 
             supervision_end_date = ?4, phone_number = ?5, relative_phone_number = ?6, address = ?7,
             file_number = ?8, is_active = ?9, notes = ?10, updated_at = CURRENT_TIMESTAMP 
             WHERE tc_no = ?11",
            params![
                convict.first_name,
                convict.last_name,
                convict.supervision_start_date,
                convict.supervision_end_date,
                convict.phone_number,
                convict.relative_phone_number,
                convict.address,
                convict.file_number,
                convict.is_active.unwrap_or(true),
                convict.notes,
                convict.tc_no
            ],
        )?;
        
        let mut stmt = conn.prepare(
            "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
             phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
             FROM convicts WHERE id = ?1"
        )?;
        let updated_convict = stmt.query_row([id], |row| row_to_convict(row))?;
        
        Ok((updated_convict, false)) // false = updated, not created
    } else {
        // Create new convict
        conn.execute(
            "INSERT INTO convicts (tc_no, first_name, last_name, supervision_start_date, supervision_end_date, phone_number, relative_phone_number, address, file_number, is_active, notes) 
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)",
            params![
                convict.tc_no,
                convict.first_name,
                convict.last_name,
                convict.supervision_start_date,
                convict.supervision_end_date,
                convict.phone_number,
                convict.relative_phone_number,
                convict.address,
                convict.file_number,
                convict.is_active.unwrap_or(true),
                convict.notes
            ],
        )?;

        let convict_id = conn.last_insert_rowid() as i32;
        
        let mut stmt = conn.prepare(
            "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
             phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
             FROM convicts WHERE id = ?1"
        )?;
        let new_convict = stmt.query_row([convict_id], |row| row_to_convict(row))?;
        
        Ok((new_convict, true)) // true = created, not updated
    }
}

pub fn search_convict_by_tc(db: &DatabaseConnection, tc_no: &str) -> Result<Option<Convict>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts WHERE tc_no = ?1"
    )?;
    
    let mut convict_iter = stmt.query_map([tc_no], |row| row_to_convict(row))?;
    
    match convict_iter.next() {
        Some(convict) => Ok(Some(convict?)),
        None => Ok(None),
    }
}

pub fn search_convicts_by_name(db: &DatabaseConnection, first_name: &str, last_name: &str) -> Result<Vec<Convict>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts 
         WHERE LOWER(first_name) LIKE LOWER(?1) AND LOWER(last_name) LIKE LOWER(?2)
         ORDER BY created_at DESC"
    )?;
    
    let search_first = format!("%{}%", first_name);
    let search_last = format!("%{}%", last_name);
    
    let convict_iter = stmt.query_map([search_first, search_last], |row| row_to_convict(row))?;
    
    let mut convicts = Vec::new();
    for convict in convict_iter {
        convicts.push(convict?);
    }
    
    Ok(convicts)
}

pub fn search_convicts_by_file_number(db: &DatabaseConnection, file_number: &str) -> Result<Vec<Convict>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts 
         WHERE file_number IS NOT NULL AND LOWER(file_number) LIKE LOWER(?1)
         ORDER BY created_at DESC"
    )?;
    
    let search_file_number = format!("%{}%", file_number);
    
    let convict_iter = stmt.query_map([search_file_number], |row| row_to_convict(row))?;
    
    let mut convicts = Vec::new();
    for convict in convict_iter {
        convicts.push(convict?);
    }
    
    Ok(convicts)
}

pub fn create_signature(db: &DatabaseConnection, signature: InsertSignature) -> Result<Signature> {
    // Validate time restrictions before creating signature
    if !validate_signature_time_restrictions(db, signature.convict_id, &signature.signature_time, &signature.signature_date)? {
        return Err(anyhow::anyhow!("İmza saati kısıtlamalarına uygun değil. Lütfen belirtilen saatlerde imza atınız."));
    }
    
    let conn = db.lock().unwrap();
    
    conn.execute(
        "INSERT INTO signatures (convict_id, signature_date, signature_time, recorded_by) 
         VALUES (?1, ?2, ?3, ?4)",
        params![
            signature.convict_id,
            signature.signature_date,
            signature.signature_time,
            signature.recorded_by
        ],
    )?;

    let signature_id = conn.last_insert_rowid() as i32;
    
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, signature_date, signature_time, recorded_by, created_at 
         FROM signatures WHERE id = ?1"
    )?;
    let signature = stmt.query_row([signature_id], |row| row_to_signature(row))?;
    
    Ok(signature)
}

pub fn get_signatures_by_convict(db: &DatabaseConnection, convict_id: i32) -> Result<Vec<Signature>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, signature_date, signature_time, recorded_by, created_at 
         FROM signatures 
         WHERE convict_id = ?1 
         ORDER BY signature_date DESC, signature_time DESC"
    )?;
    
    let signature_iter = stmt.query_map([convict_id], |row| row_to_signature(row))?;
    
    let mut signatures = Vec::new();
    for signature in signature_iter {
        signatures.push(signature?);
    }
    
    Ok(signatures)
}

// Yeni fonksiyon: Belirli bir hükümlü için belirli bir tarihte imza olup olmadığını kontrol eder
pub fn check_signature_exists(db: &DatabaseConnection, convict_id: i32, date_str: &str) -> Result<bool, rusqlite::Error> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT EXISTS(SELECT 1 FROM signatures WHERE convict_id = ?1 AND signature_date = ?2 LIMIT 1)"
    )?;
    let exists = stmt.query_row(params![convict_id, date_str], |row| row.get(0))?;
    Ok(exists)
}

// Helper function (similar to the one in commands.rs)
// This function should ideally be in a shared module if used in multiple places
// For now, we'll define it here for simplicity.
fn is_signature_due_on_date_db(date_to_check: NaiveDate, period: &SignaturePeriod) -> bool {
    if !period.is_active {
        return false;
    }

    // Ensure date strings are parsed correctly
    let period_start_date = match NaiveDate::parse_from_str(&period.start_date, "%Y-%m-%d") {
        Ok(date) => date,
        Err(_) => {
            println!("    ❌ Invalid start date format: {}", period.start_date);
            return false; // Invalid date format in period
        }
    };
    let period_end_date = match NaiveDate::parse_from_str(&period.end_date, "%Y-%m-%d") {
        Ok(date) => date,
        Err(_) => {
            println!("    ❌ Invalid end date format: {}", period.end_date);
            return false; // Invalid date format in period
        }
    };

    if date_to_check < period_start_date || date_to_check > period_end_date {
        println!("    ❌ Date {} is outside period range {} to {}",
                 date_to_check.format("%Y-%m-%d"), period.start_date, period.end_date);
        return false;
    }
    
    println!("    ✅ Date is within period range");

    match period.frequency_type.as_str() {
        // New format: "daily"
        "daily" => {
            // Every day
            true
        }
        // New format: "weekly" with frequency_value as number
        "weekly" => {
            // Check if frequency_value is a number (new format) or day names (old format)
            if let Ok(frequency_num) = period.frequency_value.parse::<i64>() {
                // New format: weekly with reference_date
                let reference_date_str = period.reference_date.as_deref().unwrap_or(&period.start_date);
                let reference_date = match NaiveDate::parse_from_str(reference_date_str, "%Y-%m-%d") {
                    Ok(date) => date,
                    Err(_) => period_start_date, // Fallback to period_start_date if reference is invalid
                };
                
                if date_to_check < reference_date { return false; }
                
                let weeks_diff = (date_to_check - reference_date).num_days() / 7;
                weeks_diff >= 0 && (weeks_diff % frequency_num == 0) &&
                (date_to_check.weekday() == reference_date.weekday())
            } else {
                // Old format: weekly with day names
                let days: Vec<&str> = period.frequency_value.split(',').map(|s| s.trim()).collect();
                let weekday_to_check = date_to_check.weekday();
                days.iter().any(|day_str| match *day_str {
                    "MONDAY" => weekday_to_check == Weekday::Mon,
                    "TUESDAY" => weekday_to_check == Weekday::Tue,
                    "WEDNESDAY" => weekday_to_check == Weekday::Wed,
                    "THURSDAY" => weekday_to_check == Weekday::Thu,
                    "FRIDAY" => weekday_to_check == Weekday::Fri,
                    "SATURDAY" => weekday_to_check == Weekday::Sat,
                    "SUNDAY" => weekday_to_check == Weekday::Sun,
                    _ => false,
                })
            }
        }
        // Old format: "WEEKLY" (all caps) - handle both numeric and day name formats
        "WEEKLY" => {
            println!("    🔍 Processing WEEKLY with value: '{}'", period.frequency_value);
            
            // First try to parse as number (new format)
            if let Ok(frequency_num) = period.frequency_value.parse::<i64>() {
                println!("    📊 Parsed as numeric format: {}", frequency_num);
                // New format: weekly with reference_date
                let reference_date_str = period.reference_date.as_deref().unwrap_or(&period.start_date);
                let reference_date = match NaiveDate::parse_from_str(reference_date_str, "%Y-%m-%d") {
                    Ok(date) => date,
                    Err(_) => period_start_date, // Fallback to period_start_date if reference is invalid
                };
                
                if date_to_check < reference_date { return false; }
                
                let weeks_diff = (date_to_check - reference_date).num_days() / 7;
                weeks_diff >= 0 && (weeks_diff % frequency_num == 0) &&
                (date_to_check.weekday() == reference_date.weekday())
            } else if period.frequency_value.starts_with('[') && period.frequency_value.ends_with(']') {
                println!("    📊 Processing bracket format: {}", period.frequency_value);
                // Format: [6] where 6 = Saturday (0=Sunday, 1=Monday, ..., 6=Saturday)
                let day_num_str = &period.frequency_value[1..period.frequency_value.len()-1];
                if let Ok(day_num) = day_num_str.parse::<u32>() {
                    let weekday_num = match date_to_check.weekday() {
                        Weekday::Sun => 0,
                        Weekday::Mon => 1,
                        Weekday::Tue => 2,
                        Weekday::Wed => 3,
                        Weekday::Thu => 4,
                        Weekday::Fri => 5,
                        Weekday::Sat => 6,
                    };
                    return weekday_num == day_num;
                }
                false
            } else {
                println!("    📊 Processing day name format: '{}'", period.frequency_value);
                // Format: day names like "SUNDAY", "MONDAY", etc.
                let weekday_to_check = date_to_check.weekday();
                let today_day_name = match weekday_to_check {
                    Weekday::Mon => "MONDAY",
                    Weekday::Tue => "TUESDAY",
                    Weekday::Wed => "WEDNESDAY",
                    Weekday::Thu => "THURSDAY",
                    Weekday::Fri => "FRIDAY",
                    Weekday::Sat => "SATURDAY",
                    Weekday::Sun => "SUNDAY",
                };
                
                println!("    📅 Today is {} ({}), period expects '{}'",
                         date_to_check.format("%A"), today_day_name, period.frequency_value.trim());
                
                let result = match period.frequency_value.trim() {
                    "MONDAY" => weekday_to_check == Weekday::Mon,
                    "TUESDAY" => weekday_to_check == Weekday::Tue,
                    "WEDNESDAY" => weekday_to_check == Weekday::Wed,
                    "THURSDAY" => weekday_to_check == Weekday::Thu,
                    "FRIDAY" => weekday_to_check == Weekday::Fri,
                    "SATURDAY" => weekday_to_check == Weekday::Sat,
                    "SUNDAY" => weekday_to_check == Weekday::Sun,
                    _ => {
                        println!("    🔍 Trying comma-separated format");
                        // Try splitting by comma for multiple days
                        let days: Vec<&str> = period.frequency_value.split(',').map(|s| s.trim()).collect();
                        days.iter().any(|day_str| match *day_str {
                            "MONDAY" => weekday_to_check == Weekday::Mon,
                            "TUESDAY" => weekday_to_check == Weekday::Tue,
                            "WEDNESDAY" => weekday_to_check == Weekday::Wed,
                            "THURSDAY" => weekday_to_check == Weekday::Thu,
                            "FRIDAY" => weekday_to_check == Weekday::Fri,
                            "SATURDAY" => weekday_to_check == Weekday::Sat,
                            "SUNDAY" => weekday_to_check == Weekday::Sun,
                            _ => false,
                        })
                    }
                };
                
                println!("    🎯 Day name match result: {}", result);
                result
            }
        }
        "EVERY_X_DAYS" => {
            let x_days = match period.frequency_value.parse::<i64>() { // Parse as i64 for num_days()
                Ok(val) if val > 0 => val,
                _ => return false, // Invalid or non-positive x_days
            };
            let reference_date_str = period.reference_date.as_deref().unwrap_or(&period.start_date);
            let reference_date = match NaiveDate::parse_from_str(reference_date_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => period_start_date, // Fallback to period_start_date if reference is invalid
            };
            if date_to_check < reference_date { return false; }
            let days_diff = (date_to_check - reference_date).num_days();
            days_diff >= 0 && (days_diff % x_days == 0)
        }
        "MONTHLY_SPECIFIC_DAYS" => {
            let days_of_month: Vec<u32> = period.frequency_value.split(',')
                .filter_map(|s| s.trim().parse::<u32>().ok())
                .collect();
            days_of_month.contains(&date_to_check.day())
        }
        _ => false,
    }
}

// Dashboard statistics
pub fn get_dashboard_stats(db: &DatabaseConnection) -> Result<DashboardStats> {
    let conn_guard = db.lock().map_err(|e| anyhow::anyhow!("Failed to acquire DB lock: {}", e.to_string()))?;
    let conn = &*conn_guard;

    let today_naive: NaiveDate = Utc::now().date_naive();
    let today_str = today_naive.format("%Y-%m-%d").to_string();
    
    println!("🔍 Dashboard Stats Debug - Today: {}", today_str);

    let total_convicts: i32 = conn.query_row(
        "SELECT COUNT(*) FROM convicts",
        [],
        |row| row.get(0)
    )?;

    let active_convicts_count: i32 = conn.query_row(
        "SELECT COUNT(*) FROM convicts WHERE is_active = 1",
        [],
        |row| row.get(0)
    )?;

    let mut expected_today_count = 0;
    let mut completed_today_count = 0;
    let mut actual_violations_today = 0;

    // Get all active convicts
    let mut stmt_convicts = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at \
         FROM convicts WHERE is_active = 1"
    )?;
    let active_convicts_iter = stmt_convicts.query_map([], row_to_convict)?;

    for convict_result in active_convicts_iter {
        let convict = convict_result?;
        
        println!("🔍 Checking convict: {} {} (ID: {})", convict.first_name, convict.last_name, convict.id);

        // Get active signature periods for this convict
        let mut stmt_periods = conn.prepare(
            "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days \
             FROM signature_periods \
             WHERE convict_id = ?1 AND is_active = 1"
        )?;
        let periods_iter = stmt_periods.query_map(params![convict.id], row_to_signature_period)?;

        let mut is_due_for_convict_today = false;
        for period_result in periods_iter {
            let period = period_result?;
            println!("  📅 Period: {} - {} ({} to {})", period.frequency_type, period.frequency_value, period.start_date, period.end_date);
            
            let is_due = is_signature_due_on_date_db(today_naive, &period);
            println!("  ⏰ Is due today? {}", is_due);
            
            if is_due {
                is_due_for_convict_today = true;
                break;
            }
        }
        
        println!("  📋 Final result for {}: is_due_today = {}", convict.first_name, is_due_for_convict_today);

        if is_due_for_convict_today {
            // Check for active exemptions for today
            let mut stmt_check_exemption = conn.prepare(
                "SELECT EXISTS(SELECT 1 FROM exemptions WHERE convict_id = ?1 AND ?2 BETWEEN start_date AND end_date AND is_active = 1 LIMIT 1)"
            )?;
            let has_exemption: bool = stmt_check_exemption.query_row(
                params![convict.id, today_str], |row| row.get(0)
            )?;

            if !has_exemption {
                expected_today_count += 1;
                
                // Check if this convict signed today
                let mut stmt_check_signature = conn.prepare(
                    "SELECT EXISTS(SELECT 1 FROM signatures WHERE convict_id = ?1 AND signature_date = ?2 LIMIT 1)"
                )?;
                let signed_today_for_this_convict: bool = stmt_check_signature.query_row(
                    params![convict.id, today_str], |row| row.get(0)
                )?;

                if signed_today_for_this_convict {
                    completed_today_count += 1;
                } else {
                    actual_violations_today += 1;
                }
            }
        }
    }
    
    println!("📊 Dashboard Stats Results:");
    println!("  Total convicts: {}", total_convicts);
    println!("  Active convicts: {}", active_convicts_count);
    println!("  Expected today: {}", expected_today_count);
    println!("  Completed today: {}", completed_today_count);
    println!("  Violations: {}", actual_violations_today);
    
    Ok(DashboardStats {
        total_convicts,
        active_convicts: active_convicts_count, // Renamed for clarity
        expected_today: expected_today_count,
        completed_today: completed_today_count,
        violations: actual_violations_today,
    })
}

// Additional utility functions for better database management
pub fn update_convict(db: &DatabaseConnection, id: i32, convict: InsertConvict) -> Result<Convict> {
    let conn = db.lock().unwrap();
    
    conn.execute(
        "UPDATE convicts SET tc_no = ?1, first_name = ?2, last_name = ?3, 
         supervision_start_date = ?4, supervision_end_date = ?5, phone_number = ?6, 
         relative_phone_number = ?7, address = ?8, file_number = ?9, is_active = ?10, 
         notes = ?11, updated_at = CURRENT_TIMESTAMP 
         WHERE id = ?12",
        params![
            convict.tc_no,
            convict.first_name,
            convict.last_name,
            convict.supervision_start_date,
            convict.supervision_end_date,
            convict.phone_number,
            convict.relative_phone_number,
            convict.address,
            convict.file_number,
            convict.is_active.unwrap_or(true),
            convict.notes,
            id
        ],
    )?;
    
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts WHERE id = ?1"
    )?;
    let convict = stmt.query_row([id], |row| row_to_convict(row))?;
    
    Ok(convict)
}

// Update signature period
pub fn update_signature_period(db: &DatabaseConnection, id: i32, period: InsertSignaturePeriod) -> Result<SignaturePeriod> {
    let conn = db.lock().unwrap();
    
    conn.execute(
        "UPDATE signature_periods SET convict_id = ?1, start_date = ?2, end_date = ?3, 
         frequency_type = ?4, frequency_value = ?5, reference_date = ?6, is_active = ?7, 
         time_start = ?8, time_end = ?9, allowed_days = ?10 
         WHERE id = ?11",
        params![
            period.convict_id,
            period.start_date,
            period.end_date,
            period.frequency_type,
            period.frequency_value,
            period.reference_date,
            period.is_active.unwrap_or(true),
            period.time_start,
            period.time_end,
            period.allowed_days,
            id
        ],
    )?;
    
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days 
         FROM signature_periods WHERE id = ?1"
    )?;
    let period = stmt.query_row([id], |row| row_to_signature_period(row))?;
    
    Ok(period)
}

// Delete signature period
pub fn delete_signature_period(db: &DatabaseConnection, id: i32) -> Result<bool> {
    let conn = db.lock().unwrap();
    
    let affected_rows = conn.execute("DELETE FROM signature_periods WHERE id = ?1", [id])?;
    
    Ok(affected_rows > 0)
}

// Delete signature
pub fn delete_signature(db: &DatabaseConnection, id: i32) -> Result<bool> {
    let conn = db.lock().unwrap();
    
    let affected_rows = conn.execute("DELETE FROM signatures WHERE id = ?1", [id])?;
    
    Ok(affected_rows > 0)
}

// Delete convict (with cascading deletes)
pub fn delete_convict(db: &DatabaseConnection, id: i32) -> Result<bool> {
    let conn = db.lock().unwrap();
    
    // Delete related records first (due to foreign key constraints)
    conn.execute("DELETE FROM signatures WHERE convict_id = ?1", [id])?;
    conn.execute("DELETE FROM signature_periods WHERE convict_id = ?1", [id])?;
    conn.execute("DELETE FROM exemptions WHERE convict_id = ?1", [id])?;
    
    // Delete the convict
    let affected_rows = conn.execute("DELETE FROM convicts WHERE id = ?1", [id])?;
    
    Ok(affected_rows > 0)
}

// Bulk delete convicts (with cascading deletes)
pub fn bulk_delete_convicts(db: &DatabaseConnection, ids: Vec<i32>) -> Result<i32> {
    println!("bulk_delete_convicts called with {} IDs: {:?}", ids.len(), ids);
    let conn = db.lock().unwrap();
    
    let mut total_deleted = 0i32;
    
    // Use transaction for bulk operations
    let tx = conn.unchecked_transaction()?;
    
    for id in ids {
        println!("Processing convict ID: {}", id);
        
        // Delete related records first (due to foreign key constraints)
        let signatures_deleted = tx.execute("DELETE FROM signatures WHERE convict_id = ?1", [id])?;
        println!("Deleted {} signatures for convict {}", signatures_deleted, id);
        
        let periods_deleted = tx.execute("DELETE FROM signature_periods WHERE convict_id = ?1", [id])?;
        println!("Deleted {} signature periods for convict {}", periods_deleted, id);
        
        // Delete exemptions (this was missing and causing the foreign key constraint failure)
        let exemptions_deleted = tx.execute("DELETE FROM exemptions WHERE convict_id = ?1", [id])?;
        println!("Deleted {} exemptions for convict {}", exemptions_deleted, id);
        
        // Delete the convict
        let affected_rows = tx.execute("DELETE FROM convicts WHERE id = ?1", [id])?;
        println!("Deleted {} convict records for ID {}", affected_rows, id);
        total_deleted += affected_rows as i32;
    }
    
    tx.commit()?;
    println!("Transaction committed. Total deleted: {}", total_deleted);
    
    Ok(total_deleted)
}

// Exemption CRUD operations
pub fn create_exemption(db: &DatabaseConnection, exemption: InsertExemption) -> Result<Exemption> {
    let conn = db.lock().unwrap();
    
    // Check for existing exemption with same convict_id, exemption_type, start_date, and end_date
    let existing_count: i32 = conn.query_row(
        "SELECT COUNT(*) FROM exemptions 
         WHERE convict_id = ?1 AND exemption_type = ?2 AND start_date = ?3 AND end_date = ?4 AND is_active = 1",
        params![
            exemption.convict_id,
            exemption.exemption_type,
            exemption.start_date,
            exemption.end_date
        ],
        |row| row.get(0)
    )?;
    
    if existing_count > 0 {
        return Err(anyhow::anyhow!(
            "Bu hükümlü için aynı tarih aralığında ve aynı türde bir muafiyet zaten mevcut"
        ));
    }
    
    conn.execute(
        "INSERT INTO exemptions (convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)",
        params![
            exemption.convict_id,
            exemption.exemption_type,
            exemption.start_date,
            exemption.end_date,
            exemption.description,
            exemption.document_path,
            exemption.created_by,
            exemption.is_active.unwrap_or(true)
        ],
    )?;

    let exemption_id = conn.last_insert_rowid() as i32;
    
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active, created_at, updated_at 
         FROM exemptions WHERE id = ?1"
    )?;
    let exemption = stmt.query_row([exemption_id], |row| row_to_exemption(row))?;
    
    Ok(exemption)
}

pub fn get_exemptions_by_convict(db: &DatabaseConnection, convict_id: i32) -> Result<Vec<Exemption>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active, created_at, updated_at 
         FROM exemptions 
         WHERE convict_id = ?1 
         ORDER BY created_at DESC"
    )?;
    
    let exemption_iter = stmt.query_map([convict_id], |row| row_to_exemption(row))?;
    
    let mut exemptions = Vec::new();
    for exemption in exemption_iter {
        exemptions.push(exemption?);
    }
    
    Ok(exemptions)
}

pub fn get_active_exemptions_by_convict_and_date(db: &DatabaseConnection, convict_id: i32, date_str: &str) -> Result<Vec<Exemption>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active, created_at, updated_at 
         FROM exemptions 
         WHERE convict_id = ?1 AND is_active = 1 AND start_date <= ?2 AND end_date >= ?2
         ORDER BY created_at DESC"
    )?;
    
    let exemption_iter = stmt.query_map(params![convict_id, date_str], |row| row_to_exemption(row))?;
    
    let mut exemptions = Vec::new();
    for exemption in exemption_iter {
        exemptions.push(exemption?);
    }
    
    Ok(exemptions)
}

pub fn update_exemption(db: &DatabaseConnection, id: i32, exemption: InsertExemption) -> Result<Exemption> {
    let conn = db.lock().unwrap();
    
    conn.execute(
        "UPDATE exemptions SET convict_id = ?1, exemption_type = ?2, start_date = ?3, end_date = ?4, 
         description = ?5, document_path = ?6, created_by = ?7, is_active = ?8, updated_at = CURRENT_TIMESTAMP 
         WHERE id = ?9",
        params![
            exemption.convict_id,
            exemption.exemption_type,
            exemption.start_date,
            exemption.end_date,
            exemption.description,
            exemption.document_path,
            exemption.created_by,
            exemption.is_active.unwrap_or(true),
            id
        ],
    )?;
    
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active, created_at, updated_at 
         FROM exemptions WHERE id = ?1"
    )?;
    let exemption = stmt.query_row([id], |row| row_to_exemption(row))?;
    
    Ok(exemption)
}

pub fn delete_exemption(db: &DatabaseConnection, id: i32) -> Result<bool> {
    let conn = db.lock().unwrap();
    
    let affected_rows = conn.execute("DELETE FROM exemptions WHERE id = ?1", [id])?;
    
    Ok(affected_rows > 0)
}

// Transaction support for complex operations
pub fn with_transaction<F, R>(db: &DatabaseConnection, f: F) -> Result<R>
where
    F: FnOnce(&Connection) -> Result<R>,
{
    let conn = db.lock().unwrap();
    
    conn.execute("BEGIN TRANSACTION", [])?;
    
    match f(&conn) {
        Ok(result) => {
            conn.execute("COMMIT", [])?;
            Ok(result)
        }
        Err(error) => {
            let _ = conn.execute("ROLLBACK", []);
            Err(error)
        }
    }
}

// Database validation and health check
pub fn validate_database_schema(db: &DatabaseConnection) -> Result<bool> {
    let conn = db.lock().unwrap();
    
    // Check if all required tables exist
    let tables = vec!["users", "convicts", "signature_periods", "signatures"];
    
    for table in tables {
        let count: i32 = conn.query_row(
            "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?1",
            [table],
            |row| row.get(0)
        )?;
        
        if count == 0 {
            return Ok(false);
        }
    }
    
    // Check foreign key constraints are enabled
    let fk_enabled: i32 = conn.query_row(
        "PRAGMA foreign_keys",
        [],
        |row| row.get(0)
    )?;
    
    if fk_enabled == 0 {
        println!("Warning: Foreign keys are not enabled");
    }
    
    Ok(true)
}

// Function to get all convicts expected to sign today (including those who have already signed)
pub fn get_all_expected_signatures_for_today_db(db: &DatabaseConnection) -> Result<Vec<ExpectedSignature>> {
    let conn_guard = db.lock().map_err(|e| anyhow::anyhow!("Failed to acquire DB lock: {}", e.to_string()))?;
    let conn = &*conn_guard;
    
    let today_naive: NaiveDate = Utc::now().date_naive();
    let today_str = today_naive.format("%Y-%m-%d").to_string();

    let mut expected_signatures = Vec::new();

    // Get all active convicts
    let mut stmt_convicts = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at \
         FROM convicts WHERE is_active = 1"
    )?;
    let convicts_iter = stmt_convicts.query_map([], row_to_convict)?;

    for convict_result in convicts_iter {
        let convict = convict_result?;

        // Get active signature periods for this convict
        let mut stmt_periods = conn.prepare(
            "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days \
             FROM signature_periods \
             WHERE convict_id = ?1 AND is_active = 1"
        )?;
        let periods_iter = stmt_periods.query_map(params![convict.id], row_to_signature_period)?;

        let mut is_due_today = false;
        for period_result in periods_iter {
            let period = period_result?;
            if is_signature_due_on_date_db(today_naive, &period) {
                is_due_today = true;
                break;
            }
        }

        if is_due_today {
            // Check for active exemptions for this convict and date
            let mut stmt_exemptions = conn.prepare(
                "SELECT COUNT(*) FROM exemptions 
                 WHERE convict_id = ?1 AND is_active = 1 AND start_date <= ?2 AND end_date >= ?2"
            )?;
            let exemption_count: i32 = stmt_exemptions.query_row(
                params![convict.id, today_str],
                |row| row.get(0)
            )?;
            
            // If convict has active exemptions, skip them from expected signatures
            if exemption_count > 0 {
                continue;
            }

            // Check if this convict has signed today
            let mut stmt_check_signature = conn.prepare(
                "SELECT signature_time FROM signatures WHERE convict_id = ?1 AND signature_date = ?2"
            )?;
            let signature_time_result = stmt_check_signature.query_row(
                params![convict.id, today_str],
                |row| row.get::<_, String>(0)
            );

            let (has_signed, signature_time) = match signature_time_result {
                Ok(time) => (true, Some(time)),
                Err(_) => (false, None),
            };

            expected_signatures.push(ExpectedSignature {
                convict_id: convict.id,
                tc_no: convict.tc_no,
                first_name: convict.first_name,
                last_name: convict.last_name,
                has_signed,
                signature_time,
            });
        }
    }

    // Sort by signature status and time
    expected_signatures.sort_by(|a, b| {
        match (&a.has_signed, &b.has_signed) {
            (false, true) => std::cmp::Ordering::Less, // Unsigned first
            (true, false) => std::cmp::Ordering::Greater,
            (true, true) => b.signature_time.cmp(&a.signature_time), // Both signed, sort by time
            (false, false) => a.last_name.cmp(&b.last_name), // Both unsigned, sort by last name
        }
    });

    Ok(expected_signatures)
}

pub fn get_violations_for_last_30_days_db(db: &DatabaseConnection) -> Result<Vec<ViolationRecord>> {
    let conn_guard = db.lock().map_err(|e| anyhow::anyhow!("Failed to acquire DB lock: {}", e.to_string()))?;
    let conn = &*conn_guard;
    
    let today = Utc::now().date_naive();
    let thirty_days_ago = today - chrono::Duration::days(30);
    
    let mut violations = Vec::new();
    let mut current_date = thirty_days_ago;
    
    while current_date <= today {
        let date_str = current_date.format("%Y-%m-%d").to_string();
        
        // Get all active convicts
        let mut stmt_convicts = conn.prepare(
            "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at \
             FROM convicts WHERE is_active = 1"
        )?;
        let convicts_iter = stmt_convicts.query_map([], row_to_convict)?;

        for convict_result in convicts_iter {
            let convict = convict_result?;
            
            // Get active signature periods for this convict
            let mut stmt_periods = conn.prepare(
                "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days \
                 FROM signature_periods \
                 WHERE convict_id = ?1 AND is_active = 1"
            )?;
            let periods_iter = stmt_periods.query_map(params![convict.id], row_to_signature_period)?;

            let mut expected_frequency = String::new();
            let mut is_due_today = false;
            
            for period_result in periods_iter {
                let period = period_result?;
                if is_signature_due_on_date_db(current_date, &period) {
                    is_due_today = true;
                    expected_frequency = match period.frequency_type.as_str() {
                        "WEEKLY" => {
                            let days: Vec<&str> = period.frequency_value.split(',').map(|s| s.trim()).collect();
                            let day_names: Vec<String> = days.iter().map(|d| match *d {
                                "MONDAY" => "Pazartesi".to_string(),
                                "TUESDAY" => "Salı".to_string(),
                                "WEDNESDAY" => "Çarşamba".to_string(),
                                "THURSDAY" => "Perşembe".to_string(),
                                "FRIDAY" => "Cuma".to_string(),
                                "SATURDAY" => "Cumartesi".to_string(),
                                "SUNDAY" => "Pazar".to_string(),
                                _ => d.to_string(),
                            }).collect();
                            format!("Haftalık: {}", day_names.join(", "))
                        },
                        "EVERY_X_DAYS" => format!("{} günde bir", period.frequency_value),
                        "MONTHLY_SPECIFIC_DAYS" => format!("Aylık: {} günleri", period.frequency_value),
                        _ => "Bilinmeyen Sıklık".to_string(),
                    };
                    break;
                }
            }

            if is_due_today {
                // Check for active exemptions for this date
                let mut stmt_check_exemption = conn.prepare(
                    "SELECT EXISTS(SELECT 1 FROM exemptions WHERE convict_id = ?1 AND ?2 BETWEEN start_date AND end_date AND is_active = 1 LIMIT 1)"
                )?;
                let has_exemption: bool = stmt_check_exemption.query_row(
                    params![convict.id, date_str], |row| row.get(0)
                )?;

                if !has_exemption {
                    // Check if signature exists for this date
                    let mut stmt_check_signature = conn.prepare(
                        "SELECT EXISTS(SELECT 1 FROM signatures WHERE convict_id = ?1 AND signature_date = ?2 LIMIT 1)"
                    )?;
                    let signed: bool = stmt_check_signature.query_row(
                        params![convict.id, date_str],
                        |row| row.get(0)
                    )?;

                    if !signed {
                        violations.push(ViolationRecord {
                            convict_id: convict.id,
                            tc_no: convict.tc_no,
                            first_name: convict.first_name,
                            last_name: convict.last_name,
                            violation_date: date_str.clone(),
                            expected_frequency,
                        });
                    }
                }
            }
        }
        
        current_date = current_date.succ_opt().unwrap_or(current_date);
    }

    // Sort violations by date (newest first)
    violations.sort_by(|a, b| b.violation_date.cmp(&a.violation_date));
    
    Ok(violations)
}

pub fn get_convict_by_id(db: &DatabaseConnection, id: i32) -> Result<Option<Convict>> {
    let conn = db.lock().unwrap();
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts WHERE id = ?1"
    )?;
    
    let mut convict_iter = stmt.query_map([id], |row| row_to_convict(row))?;
    
    match convict_iter.next() {
        Some(convict) => Ok(Some(convict?)),
        None => Ok(None),
    }
}

// Data validation and cleanup functions
pub fn validate_and_fix_signatures_data(db: &DatabaseConnection) -> Result<i32> {
    let conn = db.lock().unwrap();
    
    // Fix any TEXT values in recorded_by column by mapping them to proper user IDs
    let mut fixed_count = 0;
    
    // Get all signatures with TEXT recorded_by values
    let mut stmt = conn.prepare(
        "SELECT id, recorded_by FROM signatures WHERE typeof(recorded_by) = 'text'"
    )?;
    
    let rows: Vec<(i32, String)> = stmt.query_map([], |row| {
        Ok((row.get::<_, i32>(0)?, row.get::<_, String>(1)?))
    })?.collect::<Result<Vec<_>, _>>()?;
    
    for (signature_id, recorded_by_text) in rows {
        // Map common text values to user IDs
        let user_id = match recorded_by_text.as_str() {
            "admin" => {
                // Find admin user ID
                let admin_id: Option<i32> = conn.query_row(
                    "SELECT id FROM users WHERE username = 'admin' LIMIT 1",
                    [],
                    |row| row.get(0)
                ).optional()?;
                admin_id
            },
            "user" => {
                // Find user ID for 'user'
                let user_id: Option<i32> = conn.query_row(
                    "SELECT id FROM users WHERE username = 'user' LIMIT 1",
                    [],
                    |row| row.get(0)
                ).optional()?;
                user_id
            },
            _ => {
                // For unknown text values, set to NULL
                None
            }
        };
        
        // Update the record
        conn.execute(
            "UPDATE signatures SET recorded_by = ?1 WHERE id = ?2",
            params![user_id, signature_id]
        )?;
        
        fixed_count += 1;
    }
    
    Ok(fixed_count)
}

// Bulk import operations
pub fn bulk_create_signature_periods_with_transaction(db: &DatabaseConnection, periods: Vec<InsertSignaturePeriod>) -> Result<Vec<SignaturePeriod>> {
    with_transaction(db, |conn| {
        let mut created_periods = Vec::new();
        
        for period_data in periods {
            // Execute the insert within the transaction
            conn.execute(
                "INSERT INTO signature_periods (convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, time_start, time_end, allowed_days) 
                 VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
                rusqlite::params![
                    period_data.convict_id,
                    period_data.start_date,
                    period_data.end_date,
                    period_data.frequency_type,
                    period_data.frequency_value,
                    period_data.reference_date,
                    period_data.is_active.unwrap_or(true),
                    period_data.time_start,
                    period_data.time_end,
                    period_data.allowed_days
                ],
            )?;

            let period_id = conn.last_insert_rowid() as i32;
            
            // Query the created signature period
            let mut stmt = conn.prepare(
                "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days 
                 FROM signature_periods WHERE id = ?1"
            )?;
            
            let period = stmt.query_row([period_id], |row| row_to_signature_period(row))?;
            created_periods.push(period);
        }
        
        Ok(created_periods)
    })
}

pub fn bulk_create_exemptions_with_transaction(db: &DatabaseConnection, exemptions: Vec<InsertExemption>) -> Result<Vec<Exemption>> {
    with_transaction(db, |conn| {
        let mut created_exemptions = Vec::new();
        
        for exemption_data in exemptions {
            // Check for existing exemption with same convict_id, exemption_type, start_date, and end_date
            let existing_count: i32 = conn.query_row(
                "SELECT COUNT(*) FROM exemptions 
                 WHERE convict_id = ?1 AND exemption_type = ?2 AND start_date = ?3 AND end_date = ?4 AND is_active = 1",
                rusqlite::params![
                    exemption_data.convict_id,
                    exemption_data.exemption_type,
                    exemption_data.start_date,
                    exemption_data.end_date
                ],
                |row| row.get(0)
            )?;
            
            let exemption_id;
            
            if existing_count > 0 {
                // Find existing exemption and update it instead of creating a new one
                let mut existing_stmt = conn.prepare(
                    "SELECT id FROM exemptions 
                     WHERE convict_id = ?1 AND exemption_type = ?2 AND start_date = ?3 AND end_date = ?4 AND is_active = 1 
                     LIMIT 1"
                )?;
                
                exemption_id = existing_stmt.query_row(
                    rusqlite::params![
                        exemption_data.convict_id,
                        exemption_data.exemption_type,
                        exemption_data.start_date,
                        exemption_data.end_date
                    ],
                    |row| row.get::<_, i32>(0)
                )?;
                
                // Update the existing exemption with new data
                conn.execute(
                    "UPDATE exemptions SET description = ?1, document_path = ?2, created_by = ?3, updated_at = CURRENT_TIMESTAMP 
                     WHERE id = ?4",
                    rusqlite::params![
                        exemption_data.description,
                        exemption_data.document_path,
                        exemption_data.created_by,
                        exemption_id
                    ],
                )?;
            } else {
                // Insert new exemption
                conn.execute(
                    "INSERT INTO exemptions (convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active) 
                     VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)",
                    rusqlite::params![
                        exemption_data.convict_id,
                        exemption_data.exemption_type,
                        exemption_data.start_date,
                        exemption_data.end_date,
                        exemption_data.description,
                        exemption_data.document_path,
                        exemption_data.created_by,
                        exemption_data.is_active.unwrap_or(true)
                    ],
                )?;

                exemption_id = conn.last_insert_rowid() as i32;
            }
            
            // Query the exemption (whether created or updated)
            let mut stmt = conn.prepare(
                "SELECT id, convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active, created_at, updated_at 
                 FROM exemptions WHERE id = ?1"
            )?;
            
            let exemption = stmt.query_row([exemption_id], |row| row_to_exemption(row))?;
            created_exemptions.push(exemption);
        }
        
        Ok(created_exemptions)
    })
}

// Get all convicts with their full details (signature periods + exemptions) for bulk export
pub fn get_convict_full_details(db: &DatabaseConnection) -> Result<Vec<ConvictFullDetails>> {
    let conn = db.lock().unwrap();
    
    // First, get all convicts
    let mut stmt = conn.prepare(
        "SELECT id, tc_no, first_name, last_name, supervision_start_date, supervision_end_date, 
         phone_number, relative_phone_number, address, file_number, is_active, notes, created_at, updated_at 
         FROM convicts ORDER BY tc_no"
    )?;
    
    let convict_rows = stmt.query_map([], |row| row_to_convict(row))?;
    let mut convicts_with_details = Vec::new();
    
    for convict_result in convict_rows {
        let convict = convict_result?;
        
        // Get signature periods for this convict
        let mut periods_stmt = conn.prepare(
            "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days \
             FROM signature_periods WHERE convict_id = ?1 ORDER BY start_date"
        )?;
        
        let period_rows = periods_stmt.query_map([convict.id], |row| row_to_signature_period(row))?;
        let mut signature_periods = Vec::new();
        
        for period_result in period_rows {
            signature_periods.push(period_result?);
        }
        
        // Get exemptions for this convict
        let mut exemptions_stmt = conn.prepare(
            "SELECT id, convict_id, exemption_type, start_date, end_date, description, document_path, created_by, is_active, created_at, updated_at 
             FROM exemptions WHERE convict_id = ?1 ORDER BY start_date"
        )?;
        
        let exemption_rows = exemptions_stmt.query_map([convict.id], |row| row_to_exemption(row))?;
        let mut exemptions = Vec::new();
        
        for exemption_result in exemption_rows {
            exemptions.push(exemption_result?);
        }
        
        // Create ConvictFullDetails
        let convict_full_details = ConvictFullDetails {
            id: convict.id,
            tc_no: convict.tc_no,
            first_name: convict.first_name,
            last_name: convict.last_name,
            phone_number: convict.phone_number,
            relative_phone_number: convict.relative_phone_number,
            address: convict.address,
            file_number: convict.file_number,
            supervision_start_date: convict.supervision_start_date,
            supervision_end_date: convict.supervision_end_date,
            is_active: convict.is_active,
            notes: convict.notes,
            signature_periods: if signature_periods.is_empty() { None } else { Some(signature_periods) },
            exemptions: if exemptions.is_empty() { None } else { Some(exemptions) },
        };
        
        convicts_with_details.push(convict_full_details);
    }
    
    Ok(convicts_with_details)
}

// Time validation function for signature recording
pub fn validate_signature_time_restrictions(db: &DatabaseConnection, convict_id: i32, signature_time: &str, signature_date: &str) -> Result<bool> {
    let conn = db.lock().unwrap();
    
    // Get active signature periods for this convict
    let mut stmt = conn.prepare(
        "SELECT id, convict_id, start_date, end_date, frequency_type, frequency_value, reference_date, is_active, created_at, time_start, time_end, allowed_days 
         FROM signature_periods 
         WHERE convict_id = ?1 AND is_active = 1"
    )?;
    
    let period_iter = stmt.query_map([convict_id], |row| row_to_signature_period(row))?;
    
    let mut has_valid_period = false;
    let signature_date_parsed = NaiveDate::parse_from_str(signature_date, "%Y-%m-%d")
        .map_err(|e| anyhow::anyhow!("Invalid signature date format: {}", e))?;
    
    for period_result in period_iter {
        let period = period_result?;
        
        // Check if signature is due on this date for this period
        if !is_signature_due_on_date_db(signature_date_parsed, &period) {
            continue;
        }
        
        // Check time restrictions if they exist
        if let (Some(time_start), Some(time_end)) = (&period.time_start, &period.time_end) {
            // Parse times for comparison (format: "HH:MM")
            let signature_time_parsed = signature_time.replace(":", "");
            let time_start_parsed = time_start.replace(":", "");
            let time_end_parsed = time_end.replace(":", "");
            
            if signature_time_parsed < time_start_parsed || signature_time_parsed > time_end_parsed {
                continue; // This period doesn't allow the current time
            }
        }
        
        // Check day restrictions if they exist
        if let Some(allowed_days_json) = &period.allowed_days {
            if let Ok(allowed_days) = serde_json::from_str::<Vec<String>>(allowed_days_json) {
                let current_weekday = signature_date_parsed.weekday();
                let weekday_name = match current_weekday {
                    Weekday::Mon => "monday",
                    Weekday::Tue => "tuesday", 
                    Weekday::Wed => "wednesday",
                    Weekday::Thu => "thursday",
                    Weekday::Fri => "friday",
                    Weekday::Sat => "saturday",
                    Weekday::Sun => "sunday",
                };
                
                if !allowed_days.iter().any(|day| day.to_lowercase() == weekday_name) {
                    continue; // This period doesn't allow the current day
                }
            }
        }
        
        // If we reach here, this period allows the signature at this time/day
        has_valid_period = true;
        break;
    }
    
    Ok(has_valid_period)
}
