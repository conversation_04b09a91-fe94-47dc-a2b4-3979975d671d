const XLSX = require('xlsx');

// Test the fixed Excel import logic
function testExcelImportFixes() {
    console.log('=== Testing Excel Import Fixes ===\n');
    
    try {
        // Read the Excel file
        const workbook = XLSX.readFile('hukumlu-listesi-2025-06-05.xlsx');
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        const headers = jsonData[0];
        const dataRows = jsonData.slice(1);
        
        console.log('Testing MONTHLY_SPECIFIC validation fix...\n');
        
        // Test the specific case from row 3: AYLIK with "1,15,30"
        const row3Data = {};
        headers.forEach((header, headerIndex) => {
            row3Data[header] = dataRows[1][headerIndex]; // Row 3 is index 1 in dataRows
        });
        
        console.log('Row 3 Data:');
        console.log('  TC No:', row3Data['TC Kimlik No']);
        console.log('  Frequency Type:', row3Data['İmza Sıklığı Türü']);
        console.log('  Frequency Value:', row3Data['İmza Sıklığı Değeri']);
        
        // Test the fixed MONTHLY_SPECIFIC validation
        const frequencyValue = String(row3Data['İmza Sıklığı Değeri'] || '').trim();
        console.log('\nTesting MONTHLY_SPECIFIC validation:');
        console.log('  Raw value:', frequencyValue);
        
        if (frequencyValue.includes(',')) {
            const dayValues = frequencyValue.split(',').map(d => d.trim());
            console.log('  Split days:', dayValues);
            
            let allDaysValid = true;
            const invalidDays = [];
            
            if (dayValues.length === 0 || dayValues.some(d => d === '')) {
                allDaysValid = false;
                console.log('  Error: Empty days found');
            } else {
                for (const dayStr of dayValues) {
                    const dayValue = parseInt(dayStr);
                    console.log(`    Validating "${dayStr}" -> ${dayValue}`);
                    if (isNaN(dayValue) || dayValue < 1 || dayValue > 31) {
                        allDaysValid = false;
                        invalidDays.push(dayStr);
                    }
                }
            }
            
            if (allDaysValid) {
                console.log('  ✓ VALIDATION PASSED: All days are valid');
                console.log('  Will create', dayValues.length, 'separate signature periods:');
                dayValues.forEach(day => {
                    console.log(`    - Monthly signature on day ${day}`);
                });
            } else {
                console.log('  ✗ VALIDATION FAILED: Invalid days:', invalidDays);
            }
        } else {
            const dayValue = parseInt(frequencyValue);
            if (!isNaN(dayValue) && dayValue >= 1 && dayValue <= 31) {
                console.log('  ✓ VALIDATION PASSED: Single day is valid');
            } else {
                console.log('  ✗ VALIDATION FAILED: Invalid single day');
            }
        }
        
        console.log('\n=== Testing All Rows ===\n');
        
        let totalValidPeriods = 0;
        let totalValidExemptions = 0;
        
        dataRows.forEach((row, index) => {
            if (row.some(cell => cell !== undefined && cell !== '')) {
                console.log(`\n--- Row ${index + 2} ---`);
                
                const rowData = {};
                headers.forEach((header, headerIndex) => {
                    rowData[header] = row[headerIndex];
                });
                
                const tcNo = rowData['TC Kimlik No'];
                console.log('TC No:', tcNo);
                
                // Test signature period processing
                const frequencyType = rowData['İmza Sıklığı Türü'];
                const frequencyValue = String(rowData['İmza Sıklığı Değeri'] || '').trim();
                
                if (frequencyType && frequencyValue) {
                    console.log('Signature Period:');
                    console.log('  Type:', frequencyType);
                    console.log('  Value:', frequencyValue);
                    
                    if (frequencyType === 'HAFTALIK' && frequencyValue.includes(',')) {
                        const days = frequencyValue.split(',').map(d => d.trim());
                        console.log('  Will create', days.length, 'WEEKLY periods');
                        totalValidPeriods += days.length;
                    } else if (frequencyType === 'AYLIK' && frequencyValue.includes(',')) {
                        const days = frequencyValue.split(',').map(d => d.trim());
                        console.log('  Will create', days.length, 'MONTHLY_SPECIFIC periods');
                        totalValidPeriods += days.length;
                    } else {
                        console.log('  Will create 1 period');
                        totalValidPeriods += 1;
                    }
                }
                
                // Test exemption processing
                const exemptionType = rowData['Muafiyet Türü'];
                if (exemptionType && exemptionType.trim()) {
                    console.log('Exemption:', exemptionType);
                    totalValidExemptions += 1;
                }
            }
        });
        
        console.log('\n=== Final Summary ===');
        console.log('Total signature periods that will be created:', totalValidPeriods);
        console.log('Total exemptions that will be created:', totalValidExemptions);
        console.log('MONTHLY_SPECIFIC fix is working correctly');
        
    } catch (error) {
        console.error('Error testing Excel import:', error);
    }
}

testExcelImportFixes();
